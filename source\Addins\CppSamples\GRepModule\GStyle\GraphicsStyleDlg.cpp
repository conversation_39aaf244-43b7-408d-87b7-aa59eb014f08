﻿#include "GraphicsStyleDlg.h"
#include "IUiView.h"
#include "ICategory.h"
#include "IDocument.h"
#include "GmTitleBar.h"
#include "IUiDocument.h"
#include "UniIdentity.h"
#include "JournalUtils.h"
#include "UiCommonDialog.h"
#include "IPublishConfig.h"
#include "IJournalUiEvent.h"
#include "ICategoryLibrary.h"
#include "GmStyleSheetHelper.h"
#include "IGraphicsStyleManager.h"
#include "ICategoryAndGraphicsStyleFilter.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "IGraphicsStyleDataOverrideItems.h"
#include "qevent.h"
#include "QDialogButtonBox.h"
#include "GStyleViewerUtils.h"
#include "GraphicsStyleTable.h"
#include "Material/MaterialViewerUtils.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

using namespace gcmp;
using namespace Sample;

GraphicsStyleDlg::GraphicsStyleDlg(QWidget* pMainWindow, gcmp::IUiDocument* pUIDoc, gcmp::IUiView* currentView,
    std::wstring& title, std::vector<gcmp::OwnerPtr<Sample::GraphicsOverrideData>>& gOverrideDataList,
    std::vector<gcmp::OwnerPtr<MaterialOverrideData>>& gOverrideMaterialDataList)
    : QDialog(pMainWindow, Qt::FramelessWindowHint | Qt::WindowSystemMenuHint
        | Qt::WindowMinMaxButtonsHint)
    , m_wpUIDoc(pUIDoc)
    , m_wpCurrentView(currentView)
    , m_opApplyBntMouseEvent(nullptr)
    , m_gOverrideDataList(gOverrideDataList)
{
    m_ui.setupUi(this);
    resize(1538, 500);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pUIDoc, L"无效参数",L"GDMPLab",L"2024-03-30");
    gcmp::IDocument* pDoc = pUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-03-30");
    DBG_WARN_AND_RETURN_VOID_UNLESS(gOverrideDataList.size() == 9, L"gOverrideDataList个数不是期望的9个",L"GDMPLab",L"2024-03-30");

    // 设置对话框样式
    std::wstring styleSheetStr = GmStyleSheetHelper::Instance()->GetCurrentStyleData();
    setStyleSheet(QString::fromStdWString(styleSheetStr));
    m_ui.gbmp_title->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Fixed);
    m_ui.gbmp_title->setIconVisible(false);
    m_ui.gbmp_title->setMaxButtonVisible(false);
    m_ui.gbmp_title->setMinButtonVisible(false);
    m_ui.gbmp_title->setText(QString::fromStdWString(title));
    this->setProperty("GStyle_Title", true);
    connect(m_ui.gbmp_title, &GmTitleBar::closeRequested, this, &GraphicsStyleDlg::reject);
    connect(m_ui.gbmp_title, &GmTitleBar::moveRequested, this, &GraphicsStyleDlg::onMoveRequested);

    // 响应按钮
    DBG_WARN_AND_RETURN_VOID_UNLESS(m_ui.applyBnt != nullptr, L"m_ui.applyBnt 为空",L"GDMPLab",L"2024-03-30");
    m_ui.applyBnt->button(QDialogButtonBox::Ok)->setObjectName(QStringLiteral("buttonOK"));
    m_ui.applyBnt->button(QDialogButtonBox::Cancel)->setObjectName(QStringLiteral("buttonCancel"));
    m_ui.applyBnt->button(QDialogButtonBox::Ok)->setText(QString::fromStdWString(GBMP_TR(L"确定")));
    m_ui.applyBnt->button(QDialogButtonBox::Cancel)->setText(QString::fromStdWString(GBMP_TR(L"取消")));

    QObject::connect(m_ui.applyBnt, SIGNAL(accepted()), this, SLOT(OnApply()));

    // 初始化【投影线样式】页面
    {
        QWidget* pTabPage = NEW_AS_QT_CHILD(QWidget, m_ui.tabWidget);
        OwnerPtr<GraphicsStyleTable> opTableProjectionLines =
            NEW_AS_OWNER_PTR(GraphicsStyleTable, pUIDoc, currentView, this, GraphicsStyleTable::TableType::ProjectionLines,
                m_gOverrideDataList, gOverrideMaterialDataList);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opTableProjectionLines, L"opTableProjectionLines为空",L"GDMPLab",L"2024-03-30");
        QVBoxLayout* pVLayout = NEW_AS_QT_CHILD(QVBoxLayout, pTabPage);
        pVLayout->addWidget(opTableProjectionLines->GetWidgetFW());
        opTableProjectionLines->GetWidgetFW()->setParent(pTabPage);
        m_ui.tabWidget->addTab(pTabPage, QString::fromStdWString(L"投影线样式"));
        m_opTables.push_back(TransferOwnership(opTableProjectionLines));
    }
    // 初始化【截面线样式】页面
    {
        QWidget* pTabPage = NEW_AS_QT_CHILD(QWidget, m_ui.tabWidget);
        OwnerPtr<GraphicsStyleTable> opTableSectionLines =
            NEW_AS_OWNER_PTR(GraphicsStyleTable, pUIDoc, currentView, this, GraphicsStyleTable::TableType::SectionLines,
                m_gOverrideDataList, gOverrideMaterialDataList);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opTableSectionLines, L"opTableSectionLines为空",L"GDMPLab",L"2024-03-30");
        QVBoxLayout* pVLayout = NEW_AS_QT_CHILD(QVBoxLayout, pTabPage);
        pVLayout->addWidget(opTableSectionLines->GetWidgetFW());
        opTableSectionLines->GetWidgetFW()->setParent(pTabPage);
        m_ui.tabWidget->addTab(pTabPage, QString::fromStdWString(L"截面线样式"));
        m_opTables.push_back(TransferOwnership(opTableSectionLines));
    }
    // 初始化【投影面样式】页面
    {
        QWidget* pTabPage = NEW_AS_QT_CHILD(QWidget, m_ui.tabWidget);
        OwnerPtr<GraphicsStyleTable> opTableProjectionFaces =
            NEW_AS_OWNER_PTR(GraphicsStyleTable, pUIDoc, currentView, this, GraphicsStyleTable::TableType::ProjectionFaces,
                m_gOverrideDataList, gOverrideMaterialDataList);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opTableProjectionFaces, L"opTableProjectionFaces为空",L"GDMPLab",L"2024-03-30");
        QVBoxLayout* pVLayout = NEW_AS_QT_CHILD(QVBoxLayout, pTabPage);
        pVLayout->addWidget(opTableProjectionFaces->GetWidgetFW());
        opTableProjectionFaces->GetWidgetFW()->setParent(pTabPage);
        m_ui.tabWidget->addTab(pTabPage, QString::fromStdWString(L"投影面样式"));
        m_opTables.push_back(TransferOwnership(opTableProjectionFaces));
    }
    // 初始化【截面面样式】页面
    {
        QWidget* pTabPage = NEW_AS_QT_CHILD(QWidget, m_ui.tabWidget);
        OwnerPtr<GraphicsStyleTable> opTableSectionFaces =
            NEW_AS_OWNER_PTR(GraphicsStyleTable, pUIDoc, currentView, this, GraphicsStyleTable::TableType::SectionFaces,
                m_gOverrideDataList, gOverrideMaterialDataList);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opTableSectionFaces, L"opTableSectionFaces为空",L"GDMPLab",L"2024-03-30");
        QVBoxLayout* pVLayout = NEW_AS_QT_CHILD(QVBoxLayout, pTabPage);
        pVLayout->addWidget(opTableSectionFaces->GetWidgetFW());
        opTableSectionFaces->GetWidgetFW()->setParent(pTabPage);
        m_ui.tabWidget->addTab(pTabPage, QString::fromStdWString(L"截面面样式"));
        m_opTables.push_back(TransferOwnership(opTableSectionFaces));
    }
    // 初始化【材质】页面
    {
        QWidget* pTabPage = NEW_AS_QT_CHILD(QWidget, m_ui.tabWidget);
        OwnerPtr<GraphicsStyleTable> opTableFaces =
            NEW_AS_OWNER_PTR(GraphicsStyleTable, pUIDoc, currentView, this, GraphicsStyleTable::TableType::Material, 
                m_gOverrideDataList, gOverrideMaterialDataList);
        DBG_WARN_AND_RETURN_VOID_UNLESS(opTableFaces, L"opTableFaces为空", L"GDMPLab", L"2024-03-30");
        QVBoxLayout* pVLayout = NEW_AS_QT_CHILD(QVBoxLayout, pTabPage);
        pVLayout->addWidget(opTableFaces->GetWidgetFW());
        opTableFaces->GetWidgetFW()->setParent(pTabPage);
        m_ui.tabWidget->addTab(pTabPage, QString::fromStdWString(L"材质"));
        m_opTables.push_back(TransferOwnership(opTableFaces));
    }
    m_ui.tabWidget->removeTab(0);
}

GraphicsStyleDlg::~GraphicsStyleDlg()
{
}

void GraphicsStyleDlg::OnApply()
{
    for (int i = 0; i < (int)m_opTables.size(); ++i)
    {
        if (m_opTables[i] == nullptr)
        {
            continue;
        }
        m_opTables[i]->OnApplied();
    }
}

void GraphicsStyleDlg::onMoveRequested(const QPoint& cursorPos, const QPoint& offset)
{
    Q_UNUSED(cursorPos);
    move(pos() + offset);
}



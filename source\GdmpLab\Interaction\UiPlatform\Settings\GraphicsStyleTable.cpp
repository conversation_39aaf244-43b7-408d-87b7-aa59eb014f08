﻿#include <QComboBox>
#include <QColorDialog>
#include <qlineedit.h>
#include "GraphicsStyleTable.h"
#include "GraphicsStyleDlg.h"
#include "ICategory.h"
#include "ICategoryLibrary.h"
#include "LineTypeDlg.h"
#include "UiCommonDialog.h"
#include "IGraphicsMaterialData.h"
#include "GbmpUiPlatformCommandIds.h"
#include "IUiDocument.h"
#include "IUiView.h"
#include "IPublishConfig.h"
#include "LineStyleDlg.h"
#include <QPainter>
#include "ILine3d.h"
#include "IGraphicsMaterial.h"
#include "IModelView.h"
#include "IGraphicsStyleDataOverrideItems.h"
#include "FaceStyleSettingDialog.h"
#include "JournalUtils.h"
#include "IJournalCommandData.h"
#include "ColorSettingInquiryDialog.h"
#include "IFaceHatchPattern.h"
#include "IGraphicsStyle.h"
#include "IGraphicsStyleData.h"
#include "IGraphicsStyleManager.h"
#include "IGraphicsMaterialManager.h"
#include "CommandParameters.h"
#include "FilePath.h"
#include "GbmpFileSystem.h"
#include "FileUtility.h"
#include "IDocument.h"
#include "UniIdentity.h"
#include "IElementBasicInformation.h"
#include "IFillPattern.h"
#include "IFillPatternData.h"
#include "GcmpBuiltInCategoryUniIdentities.h"
#include "IElement.h"
#include "IGenericElement.h"
#include "IUiDocumentViewManager.h"
#include "ILineTypeNew.h"
#include "ILineTypeData.h"
#include "IModelView.h"
#include "QCheckBox.h"
#include "EnableCompileWarning_The_LAST_IncludeInCpp.h"

#define MORE_MATERIAL GBMP_TR(L"更多")
#define NO_MATERIAL GBMP_TR(L"无")

using namespace gcmp;
using namespace ggp;

EXPOSE_DEBUG_MODE_EXPORT(DARKMATERIALSELECTIONDLG, GCMP_GUI_INTERFACE_EXPORT)

namespace
{
    const int buttonOffset = 1000;
}
static QString STR(std::wstring str)
{
    return QString::fromStdWString(str);
}

static QString STR(std::string str)
{
    return QString::fromStdString(str);
}


class DrawLineStyleDelegate : public QItemDelegate
{

public:
    DrawLineStyleDelegate(GraphicsStyleTable *parent = 0)
        : QItemDelegate(parent)
        , m_pParent(parent)
    {
    }

    QWidget *createEditor(QWidget *parent, const QStyleOptionViewItem &option, const QModelIndex &index) const
    {
        return nullptr;
    }

    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const
    {

        QItemDelegate::paint(painter, option, index);

        int row = index.row();
        int column = index.column();
        int cellIndex = column*buttonOffset + row;
        DBG_WARN_AND_RETURN_VOID_UNLESS(m_pParent != nullptr, L"m_pParent 不能为空",L"GDMPLab",L"2024-03-30");
        std::map<int, LineStyle> lineStyleMap = GetLineStyleMap();
        auto it = lineStyleMap.find(cellIndex);
        if (it == lineStyleMap.end())
        {
            return;
        }

        LineStyle linStyle = it->second;

        bool hasColorOverride = linStyle.hasColorOverride;
        bool hasLineWidthOverride = false;
        if (linStyle.lineWidth != -1)
        {
            hasLineWidthOverride = true;
        }
        bool hasLineTypeOverride = false;
        if (linStyle.lineType != GBMP_TR(L"无替换"))
        {
            hasLineTypeOverride = true;
        }

        if (!(hasColorOverride || hasLineTypeOverride || hasLineWidthOverride))
        {
            return;
        }

        QPen pen;
        if (hasColorOverride)
        {
            pen.setColor(QColor(linStyle.lineColor.R, linStyle.lineColor.G, linStyle.lineColor.B));
        }
        else
        {
            pen.setColor(QColor(linStyle.oldLineColor.R, linStyle.oldLineColor.G, linStyle.oldLineColor.B));
        }

        if (hasLineWidthOverride)
        {
            pen.setWidth((int)linStyle.lineWidth);
        }
        else
        {
            if ((int)linStyle.oldLineWidth > 12)
            {
                pen.setWidth(1);
            }
            else
            {
                pen.setWidth((int)linStyle.oldLineWidth);
            }

        }

        painter->setPen(pen);

        // 构造待离散的预览直线
        double height = option.rect.top() + option.rect.bottom();
        double lineHeight = height * 0.5;
        OwnerPtr<ILine3d> lineToDiscrete = ILine3d::Create(Vector3d(option.rect.left(), lineHeight, 0.0), Vector3d(option.rect.right(), lineHeight, 0.0));

        std::wstring lineTypeName;
        if (hasLineTypeOverride)
        {
            lineTypeName = linStyle.lineType;
        }
        else
        {
            lineTypeName = linStyle.oldLineType;
        }

        if (lineTypeName == L"")
        {
            lineTypeName = L"StraightLine";
        }

        // 通过线型名字，获得线型对象
        //OwnerPtr<ILineType> opLineType = ILineType::GetLineType(lineTypeName);
        //DBG_WARN_AND_RETURN_VOID_UNLESS(opLineType.get(), L"LineType is null", L"GDMPLab", L"2023-12-20");
        //std::vector<Vector3d> vertexList, pointList;
        //if (opLineType.get() != nullptr)
        //{
        //    // 经验值？
        //    double tmpScale = -1.0;
        //    opLineType->Discrete(lineToDiscrete.get(), tmpScale, vertexList, pointList);
        //}

        IUiDocumentViewManager* pUiDocumentViewManager = IUiDocumentViewManager::Get();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pUiDocumentViewManager, L"pUiDocumentViewManager为空",L"GDMPLab",L"2024-03-30");
        IUiView* pUIView = pUiDocumentViewManager->GetCurrentUiView();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pUIView, L"pUIView为空",L"GDMPLab",L"2024-03-30");
        IUiDocument* pUIDoc = pUIView->GetUiDocument();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pUIDoc, L"pUIDoc为空",L"GDMPLab",L"2024-03-30");
        IDocument* pDoc = pUIDoc->GetDbDocument();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"pDoc为空",L"GDMPLab",L"2024-03-30");
        const ILineTypeNew* pLineType = ILineTypeNew::GetLineTypeByName(pDoc, lineTypeName);
        DBG_WARN_AND_RETURN_VOID_UNLESS(pLineType, L"没找到对应线型",L"GDMPLab",L"2024-03-30");
        const ILineTypeData* plnTypeData = pLineType->GetLineTypeData();
        DBG_WARN_AND_RETURN_VOID_UNLESS(plnTypeData, L"ILineTypeData为空",L"GDMPLab",L"2024-03-30");
        std::vector<Vector3d> vertexList, pointList, textTriangles;
        plnTypeData->Discrete(lineToDiscrete.get(), -1, vertexList, pointList, textTriangles, L"");

        if (!vertexList.empty())
        {
            for (int i = 0; i < (int)vertexList.size() - 1;)
            {
                painter->drawLine(QPointF(vertexList[i].X(), height - vertexList[i].Y()), QPointF(vertexList[i + 1].X(), height -vertexList[i + 1].Y()));
                i += 2;
            }
        }

        QVector<QPointF> qPointList;
        for each (auto& itr in pointList)
        {
            qPointList.push_back(QPointF(itr.X(), itr.Y()));
        }
        painter->drawPoints(qPointList);

        /*
        if (!textTriangles.empty())
        {
            painter->setRenderHint(QPainter::Antialiasing);
            painter->setRenderHint(QPainter::TextAntialiasing);
            for (size_t i = 0; i < textTriangles.size(); i = i + 3)
            {
                QPainterPath path;
                path.moveTo(QPointF(textTriangles[i].GetX(), height - textTriangles[i].GetY()));
                path.lineTo(QPointF(textTriangles[i + 1].GetX(),height - textTriangles[i + 1].GetY()));
                path.lineTo(QPointF(textTriangles[i + 2].GetX(),height - textTriangles[i + 2].GetY()));
                path.lineTo(QPointF(textTriangles[i].GetX(), height - textTriangles[i].GetY()));
                painter->drawPath(path);
            }
        }
        */
    }

    std::map<int, LineStyle> GetLineStyleMap() const
    {
        return m_pParent->GetLineStyleMap();
    }

private:
    GraphicsStyleTable* m_pParent;
};

LineStyle::LineStyle() :
    lineWidth(-1),
    hasColorOverride(false),
    oldLineWidth(-1)
{

}


//////////////////////////////////////////////////////////////////////////

void FaceHatchData::GetDataFrom(const gcmp::IGraphicsStyle& gstyleElemet, const IModelView* pModelView)
{
    if (pModelView != nullptr) //视图级
    {
        auto opGStyleDataTmp = pModelView->GetGraphicsStyleOverrideByCategoryUid(gstyleElemet.GetTargetCategoryUid());
        m_projectionColor = opGStyleDataTmp.second->GetProjectionFaceHatchColor();
        m_projectionPattern = opGStyleDataTmp.second->GetProjectionFaceHatchPattern();
        m_projectionAngle = opGStyleDataTmp.second->GetProjectionFaceHatchRotation();
        m_projectionScale = opGStyleDataTmp.second->GetProjectionFaceHatchScale();
        m_isProjectionPatternOverride = opGStyleDataTmp.first->IsProjectionFaceHatchPatternOverridden();
        m_sectionColor = opGStyleDataTmp.second->GetSectionFaceHatchColor();
        m_sectionPattern = opGStyleDataTmp.second->GetSectionFaceHatchPattern();
        m_sectionAngle = opGStyleDataTmp.second->GetSectionFaceHatchRotation();
        m_sectionScale = opGStyleDataTmp.second->GetSectionFaceHatchScale();
        m_isSectionPatternOverride = opGStyleDataTmp.first->IsSectionFaceHatchPatternOverridden();
        m_isProjectionColorOverride = opGStyleDataTmp.first->IsProjectionFaceHatchColorOverridden();
    }
    else
    {
        OwnerPtr<IGraphicsStyleData> opGStyleData = gstyleElemet.GetGraphicsStyleData();
        DBG_WARN_AND_RETURN_VOID_UNLESS(opGStyleData.get(), L"opGStyleData 空指针",L"GDMPLab",L"2024-03-30");

        m_projectionColor = opGStyleData->GetProjectionFaceHatchColor();
        m_projectionPattern = opGStyleData->GetProjectionFaceHatchPattern();
        m_projectionAngle = opGStyleData->GetProjectionFaceHatchRotation();
        m_projectionScale = opGStyleData->GetProjectionFaceHatchScale();
        m_sectionColor = opGStyleData->GetSectionFaceHatchColor();
        m_sectionPattern = opGStyleData->GetSectionFaceHatchPattern();
        m_sectionAngle = opGStyleData->GetSectionFaceHatchRotation();
        m_sectionScale = opGStyleData->GetSectionFaceHatchScale();
    }
}

void FaceHatchData::SetDataTo(gcmp::IGraphicsStyle& gstyleElement, gcmp::IModelView* pModelView) const
{
    if (pModelView != nullptr) //视图级
    {
        auto opGStyleDataTmp = pModelView->GetGraphicsStyleOverrideByCategoryUid(gstyleElement.GetTargetCategoryUid());
        opGStyleDataTmp.second->SetProjectionFaceHatchColor(m_projectionColor);
        opGStyleDataTmp.second->SetProjectionFaceHatchPattern(m_projectionPattern);
        opGStyleDataTmp.second->SetProjectionFaceHatchRotation(m_projectionAngle);
      //  opGStyleDataTmp.second->SetProjectionFaceHatchScale(m_projectionScale);

        opGStyleDataTmp.first->OverrideProjectionFaceHatchPattern(m_isProjectionPatternOverride);
        opGStyleDataTmp.second->SetSectionFaceHatchColor(m_sectionColor);
        opGStyleDataTmp.second->SetSectionFaceHatchPattern(m_sectionPattern);
        opGStyleDataTmp.second->SetSectionFaceHatchRotation(m_sectionAngle);
    //    opGStyleDataTmp.second->SetSectionFaceHatchScale(m_sectionScale);
        opGStyleDataTmp.first->OverrideProjectionFaceHatchColor(m_isProjectionColorOverride);

        opGStyleDataTmp.first->OverrideSectionFaceHatchPattern(m_isSectionPatternOverride);

        pModelView->OverrideGraphicsStyleByCategoryUid(gstyleElement.GetTargetCategoryUid(), *opGStyleDataTmp.first, *opGStyleDataTmp.second);
    }
    else  // 文档级
    {
        OwnerPtr<IGraphicsStyleData> opGStyleData = gstyleElement.GetGraphicsStyleData();
        DBG_WARN_AND_RETURN_VOID_UNLESS(opGStyleData.get(), L"opGStyleData 空指针",L"GDMPLab",L"2024-03-30");

        opGStyleData->SetProjectionFaceHatchColor(m_projectionColor);
        opGStyleData->SetProjectionFaceHatchPattern(m_projectionPattern);
        opGStyleData->SetProjectionFaceHatchRotation(m_projectionAngle);
       // opGStyleData->SetProjectionFaceHatchScale(m_projectionScale);

        opGStyleData->SetSectionFaceHatchColor(m_sectionColor);
        opGStyleData->SetSectionFaceHatchPattern(m_sectionPattern);

        opGStyleData->SetSectionFaceHatchRotation(m_sectionAngle);
     //   opGStyleData->SetSectionFaceHatchScale(m_sectionScale);
        gstyleElement.SetGraphicsStyleData(*opGStyleData);
    }
}
//////////////////////////////////////////////////////////////////////////

SelectGColorDlg::SelectGColorDlg(const QColor& color, QPushButton* bnt, QWidget *parent)
    : QDialog(parent),
    qcolor(color),
    colorShowing(bnt),
    noOverride(false)
{
    m_ui.setupUi(this);
    QObject::connect(m_ui.noOverrideBnt, SIGNAL(clicked()), this, SLOT(SelectNoOverride()));
    QObject::connect(m_ui.selectColorBnt, SIGNAL(clicked()), this, SLOT(SelectColor()));
}

bool SelectGColorDlg::SelectNoOverride()
{
    //throw std::exception("The method or operation is not implemented.");
    //设置pGStyleElement的颜色状态为no override
    noOverride = true;
    //设置颜色显示按钮为"不显示"
    if (colorShowing != nullptr)
    {
        colorShowing->setText(STR(GBMP_TR(L"不重载")));
        colorShowing->setStyleSheet("{background-color:transparent}");
    }

    // 关闭辅助对话框
    this->close();
    return true;
}

bool SelectGColorDlg::SelectColor()
{
    gcmp::OwnerPtr<QColorDialog> colorwidget(NEW_AS_OWNER_PTR(QColorDialog));
    colorwidget->setCurrentColor(qcolor);
    QColor resColor;
    resColor = colorwidget->getColor(qcolor, this);
    if (colorShowing != nullptr)
    {
        colorShowing->setStyleSheet(QString("background-color:%1").arg(resColor.name()));
        //删除默认文字
        if (colorShowing->text() == STR(GBMP_TR(L"不重载")))
        {
            colorShowing->setText(QString::Null());
        }
    }
    m_resColor = resColor;
    this->close();
    return true;
}

GraphicsStyleTable::GraphicsStyleTable(gcmp::IUiDocument* pUIDoc, gcmp::IUiView* currentView,
    QDialog* parent, const std::vector<gcmp::UniIdentity>& usedCategories, const std::wstring& label/* = L""*/)
    : m_label(label)
    , m_parent(parent)
    , m_categories(usedCategories)
    , m_wpUIDoc(pUIDoc)
    , m_wpCurrentView(currentView)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(m_wpUIDoc, L"m_wpUIDoc == nullptr",L"GDMPLab",L"2024-03-30");
    gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-03-30");

    m_opColorBtnGroup = NEW_AS_OWNER_PTR(QButtonGroup);
    m_opLineTypeBtnGroup = NEW_AS_OWNER_PTR(QButtonGroup);
    m_opFaceStyleBtnGroup = NEW_AS_OWNER_PTR(QButtonGroup);

    // 获取样式类型
    int numCategory = (int)m_categories.size();
    m_materialIds.resize(numCategory);
    m_faceColor.resize(numCategory, QColor(0, 0, 0, 255));
    m_sectionFaceColor.resize(numCategory, QColor(0, 0, 0, 255));
    m_isFaceColorOverride.resize(numCategory, false);
    m_isSectionFaceColorOverride.resize(numCategory, false);
    m_faceHatchData.resize(numCategory);

    // 项目对象Tab页
    m_table = NEW_AS_QT_CHILD(QTableWidget, m_parent);
    m_table->setObjectName(QStringLiteral("GraphicsStyleTable"));
    m_table->verticalHeader()->setHidden(true);
    m_table->horizontalHeader()->setDefaultSectionSize(100);
    m_table->horizontalHeader()->setStretchLastSection(true);
    m_table->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    QObject::connect(m_table, SIGNAL(cellClicked(int, int)), this, SLOT(OnClickCell(int, int)));
    QObject::connect(m_opColorBtnGroup.get(), SIGNAL(buttonClicked(int)), this, SLOT(OnClickCell(int)));
    QObject::connect(m_opFaceStyleBtnGroup.get(), SIGNAL(buttonClicked(int)), this, SLOT(OnClickCell_faceStyle(int)));
    QObject::connect(m_opLineTypeBtnGroup.get(), SIGNAL(buttonClicked(int)), this, SLOT(OnClickLineTypeCell(int)));


    ////////////////////////////////////////////////////////////////////////////////////
    // 设置样式表头
    if (IsViewGraphicsStyle()) //视图样式
    {
        m_table->setRowCount(numCategory);
        m_table->setColumnCount(13);

        QStringList header;
        header << STR(GBMP_TR(L"可见性")) << STR(GBMP_TR(L"类别")) << STR(GBMP_TR(L"投影线")) << STR(GBMP_TR(L"截面线")) << STR(GBMP_TR(L"隐藏线")) << STR(GBMP_TR(L"投影面颜色")) << STR(GBMP_TR(L"截面颜色")) << STR(GBMP_TR(L"投影面填充图案")) 
            << STR(GBMP_TR(L"截面填充图案")) << STR(GBMP_TR(L"面透明度")) << STR(GBMP_TR(L"详细程度")) << STR(GBMP_TR(L"投影面填充颜色")) << STR(GBMP_TR(L"上投影线"));
        m_table->setHorizontalHeaderLabels(header);
        m_table->model()->setHeaderData(0, Qt::Horizontal, STR(GBMP_TR(L"可见性")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(1, Qt::Horizontal, STR(GBMP_TR(L"类别")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(2, Qt::Horizontal, STR(GBMP_TR(L"投影线")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(3, Qt::Horizontal, STR(GBMP_TR(L"截面线")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(4, Qt::Horizontal, STR(GBMP_TR(L"隐藏线")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(5, Qt::Horizontal, STR(GBMP_TR(L"投影面颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(6, Qt::Horizontal, STR(GBMP_TR(L"截面颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(7, Qt::Horizontal, STR(GBMP_TR(L"投影面填充图案")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(8, Qt::Horizontal, STR(GBMP_TR(L"截面填充图案")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(9, Qt::Horizontal, STR(GBMP_TR(L"面透明度")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(10, Qt::Horizontal, STR(GBMP_TR(L"详细程度")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(11, Qt::Horizontal, STR(GBMP_TR(L"投影面填充颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(12, Qt::Horizontal, STR(GBMP_TR(L"上投影线")), Qt::AccessibleDescriptionRole);

        m_table->horizontalHeader()->setSectionResizeMode(1, QHeaderView::ResizeToContents);

        DrawLineStyleDelegate* pItemDelegateLineType = NEW_AS_QT_CHILD(DrawLineStyleDelegate, this);
        m_table->setItemDelegateForColumn(2, pItemDelegateLineType);

        DrawLineStyleDelegate* pItemDelegateLineType2 = NEW_AS_QT_CHILD(DrawLineStyleDelegate, this);
        m_table->setItemDelegateForColumn(3, pItemDelegateLineType2);

        // 隐藏线
        DrawLineStyleDelegate* pItemDelegateLineType3 = NEW_AS_QT_CHILD(DrawLineStyleDelegate, this);
        m_table->setItemDelegateForColumn(4, pItemDelegateLineType3);

        // 上投影线
        DrawLineStyleDelegate* pItemDelegateLineType4 = NEW_AS_QT_CHILD(DrawLineStyleDelegate, this);
        m_table->setItemDelegateForColumn(12, pItemDelegateLineType4);
    }
    else//文档样式
    {
        m_table->setColumnCount(17);
        m_table->setRowCount(numCategory);
        QStringList header;
        header << STR(GBMP_TR(L"类别")) << STR(GBMP_TR(L"投影线宽")) << STR(GBMP_TR(L"线颜色")) << STR(GBMP_TR(L"线型")) << STR(GBMP_TR(L"截面线宽")) << STR(GBMP_TR(L"线颜色")) << STR(GBMP_TR(L"线型")) 
            << STR(GBMP_TR(L"隐藏线宽")) << STR(GBMP_TR(L"线颜色")) << STR(GBMP_TR(L"线型")) << STR(GBMP_TR(L"材质")) << STR(GBMP_TR(L"面样式")) << STR(GBMP_TR(L"透明度")) << STR(GBMP_TR(L"投影线颜色A有效"))
            << STR(GBMP_TR(L"上投影线宽")) << STR(GBMP_TR(L"线颜色")) << STR(GBMP_TR(L"线型"));
        m_table->setHorizontalHeaderLabels(header);
        m_table->model()->setHeaderData(0, Qt::Horizontal, STR(GBMP_TR(L"category")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(1, Qt::Horizontal, STR(GBMP_TR(L"投影线宽")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(2, Qt::Horizontal, STR(GBMP_TR(L"投影线颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(3, Qt::Horizontal, STR(GBMP_TR(L"投影线型")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(4, Qt::Horizontal, STR(GBMP_TR(L"截面线宽")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(5, Qt::Horizontal, STR(GBMP_TR(L"截面颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(6, Qt::Horizontal, STR(GBMP_TR(L"截面线型")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(7, Qt::Horizontal, STR(GBMP_TR(L"隐藏线宽")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(8, Qt::Horizontal, STR(GBMP_TR(L"隐藏颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(9, Qt::Horizontal, STR(GBMP_TR(L"隐藏线型")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(10, Qt::Horizontal, STR(GBMP_TR(L"材质")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(11, Qt::Horizontal, STR(GBMP_TR(L"面样式")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(12, Qt::Horizontal, STR(GBMP_TR(L"透明度")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(13, Qt::Horizontal, STR(GBMP_TR(L"投影线颜色A有效")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(14, Qt::Horizontal, STR(GBMP_TR(L"上投影线宽")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(15, Qt::Horizontal, STR(GBMP_TR(L"上投影线颜色")), Qt::AccessibleDescriptionRole);
        m_table->model()->setHeaderData(16, Qt::Horizontal, STR(GBMP_TR(L"上投影线型")), Qt::AccessibleDescriptionRole);
    }

    std::set<UniIdentity> allVisibleCategories = GraphicsStyleDlg::GetAllCategoriesWithGStyles(pDoc);
    IGraphicsStyleManager* pGStyleManager = pDoc->GetGraphicsStyleManager();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGStyleManager, L"pGStyleManager 为空",L"GDMPLab",L"2024-03-30");

    // 设置样式表内容
    int categoryIndex = -1;
    FOR_EACH(categoryUid, m_categories) // 这一页准备显示的类别
    {
        // 看看文档中是否有这个类别
        bool bExistInDoc = std::find(allVisibleCategories.begin(), allVisibleCategories.end(), categoryUid) != allVisibleCategories.end();

        // 显示这个类别
        if (bExistInDoc)
        {
            ++categoryIndex;

            ElementId gsID = pGStyleManager->GetGraphicsStyleIdByCategoryUid(categoryUid);
            IGraphicsStyle* pDocGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(gsID));
            DBG_WARN_AND_CONTINUE_UNLESS(pDocGStyle, L"IGraphicsStyle is null",L"GDMPLab",L"2024-03-30");
            OwnerPtr<IGraphicsStyleData> opDocGStyleData = pDocGStyle->GetGraphicsStyleData();

            //将visibilityGraphics里的成员显示到界面
            const ICategory* category = ICategoryLibrary::Get(pDoc)->GetCategory(categoryUid);
            DBG_WARN_AND_CONTINUE_UNLESS(category, L"category为空",L"GDMPLab",L"2024-03-30");
            std::wstring categoryName = category->GetDisplayName();
            OwnerPtr<QTableWidgetItem> opVerticalHeaderItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
            opVerticalHeaderItem->setText(QString::number(categoryIndex + 1, 10));
            m_table->setVerticalHeaderItem(categoryIndex, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opVerticalHeaderItem));
            m_table->model()->setHeaderData(categoryIndex, Qt::Vertical, QString::fromStdWString(categoryName), Qt::AccessibleDescriptionRole);

            if (IsViewGraphicsStyle())//视图样式初始化
            {
                IModelView *pModelView = GetModelView();
                DBG_WARN_AND_CONTINUE_UNLESS(pModelView, L"dynamic_cast<ModelView*>(GetModelView()) 返回空指针",L"GDMPLab",L"2024-03-30");

                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp =
                    pModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                OwnerPtr<IGraphicsStyleDataOverrideItems>& opViewGStyleOverriddenItems = opCurViewGStyleTmp.first;
                OwnerPtr<IGraphicsStyleData>& opViewGStyleData = opCurViewGStyleTmp.second;

                //添加可见性
                {
                    m_table->horizontalHeader()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
                    AddVisibilityCtrlToTable(m_table, pModelView, categoryUid, categoryIndex, 0);
                }

                //样式名称
                {
                    gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(categoryName));
                    opItem->setFlags(Qt::ItemIsEnabled);
                    m_table->setItem(categoryIndex, 1, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
                }

                //投影线
                {
                    bool isProjectOverride = false;
                    isProjectOverride = opViewGStyleOverriddenItems->IsProjectionLineColorOverridden() || opViewGStyleOverriddenItems->IsProjectionLineWidthOverridden()
                        || opViewGStyleOverriddenItems->IsProjectionLineTypeNameOverridden();

                    // 直接都用视图的样式数据，因为如果视图没有重载，视图样式数据返回就是文档级的样式数据？
                    LineStyle lineSyle;
                    if (opViewGStyleOverriddenItems->IsProjectionLineWidthOverridden())
                    {
                        lineSyle.lineWidth = opViewGStyleData->GetProjectionLineWidth();
                    }
                    else
                    {
                        lineSyle.lineWidth = -1.0;
                        lineSyle.oldLineWidth = opDocGStyleData->GetProjectionLineWidth();
                    }

                    if (opViewGStyleOverriddenItems->IsProjectionLineColorOverridden())
                    {
                        lineSyle.lineColor = opViewGStyleData->GetProjectionLineColor();
                        lineSyle.hasColorOverride = true;
                    }
                    else
                    {
                        lineSyle.hasColorOverride = false;
                        lineSyle.oldLineColor = opDocGStyleData->GetProjectionLineColor();
                    }

                    if (opViewGStyleOverriddenItems->IsProjectionLineTypeNameOverridden())
                    {
                        lineSyle.lineType = opViewGStyleData->GetProjectionLineTypeName();
                    }
                    else
                    {
                        lineSyle.lineType = std::wstring(GBMP_TR(L"无替换"));
                        lineSyle.oldLineType = opDocGStyleData->GetProjectionLineTypeName();
                    }

                    // QModelIndex modelIndex(categoryIndex,1);

                    int cellIndex = buttonOffset * 2 + categoryIndex;
                    m_cellIndexLineStyleMap[cellIndex] = lineSyle;

                    if (!isProjectOverride)
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 2, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                    else
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 2, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                }

                //截面线
                {
                    LineStyle lineSyle;
                    if (opViewGStyleOverriddenItems->IsSectionLineWidthOverridden())
                    {
                        lineSyle.lineWidth = opViewGStyleData->GetSectionLineWidth();
                    }
                    else
                    {
                        lineSyle.lineWidth = -1.0;
                        lineSyle.oldLineWidth = opDocGStyleData->GetProjectionLineWidth();
                    }

                    if (opViewGStyleOverriddenItems->IsSectionLineColorOverridden())
                    {
                        lineSyle.lineColor = opViewGStyleData->GetSectionLineColor();
                        lineSyle.hasColorOverride = true;
                    }
                    else
                    {
                        lineSyle.oldLineColor = opDocGStyleData->GetSectionLineColor();
                        lineSyle.hasColorOverride = false;
                    }

                    if (opViewGStyleOverriddenItems->IsSectionLineTypeNameOverridden())
                    {
                        lineSyle.lineType = opViewGStyleData->GetSectionLineTypeName();
                    }
                    else
                    {
                        lineSyle.lineType = std::wstring(GBMP_TR(L"无替换"));
                        lineSyle.oldLineType = opDocGStyleData->GetSectionLineTypeName();
                    }

                    int cellIndex = buttonOffset * 3 + categoryIndex;
                    m_cellIndexLineStyleMap[cellIndex] = lineSyle;
                    bool isProjectOverride = false;
                    isProjectOverride = opViewGStyleOverriddenItems->IsSectionLineColorOverridden() || opViewGStyleOverriddenItems->IsSectionLineWidthOverridden()
                        || opViewGStyleOverriddenItems->IsSectionLineTypeNameOverridden();

                    if (!isProjectOverride)
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 3, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                    else
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 3, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                }

                //隐藏线
                {
                    LineStyle lineSyle;
                    if (opViewGStyleOverriddenItems->IsHiddenLineWidthOverridden())
                    {
                        lineSyle.lineWidth = opViewGStyleData->GetHiddenLineWidth();
                    }
                    else
                    {
                        lineSyle.lineWidth = -1.0;
                        lineSyle.oldLineWidth = opDocGStyleData->GetHiddenLineWidth();
                    }

                    if (opViewGStyleOverriddenItems->IsHiddenLineColorOverridden())
                    {
                        lineSyle.lineColor = opViewGStyleData->GetHiddenLineColor();
                        lineSyle.hasColorOverride = true;
                    }
                    else
                    {
                        lineSyle.oldLineColor = opDocGStyleData->GetHiddenLineColor();
                        lineSyle.hasColorOverride = false;
                    }

                    if (opViewGStyleOverriddenItems->IsHiddenLineTypeNameOverridden())
                    {
                        lineSyle.lineType = opViewGStyleData->GetHiddenLineTypeName();
                    }
                    else
                    {
                        lineSyle.lineType = std::wstring(GBMP_TR(L"无替换"));
                        lineSyle.oldLineType = opDocGStyleData->GetHiddenLineTypeName();
                    }

                    int cellIndex = buttonOffset * 4 + categoryIndex;
                    m_cellIndexLineStyleMap[cellIndex] = lineSyle;
                    bool isProjectOverride = false;
                    isProjectOverride = opViewGStyleOverriddenItems->IsHiddenLineColorOverridden() || opViewGStyleOverriddenItems->IsHiddenLineWidthOverridden()
                        || opViewGStyleOverriddenItems->IsHiddenLineTypeNameOverridden();

                    if (!isProjectOverride)
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 4, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                    else
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 4, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                }

                // 投影面颜色
                {
                    m_faceColor[categoryIndex] = QColor(opViewGStyleData->GetColor().R, opViewGStyleData->GetColor().G, opViewGStyleData->GetColor().B, opViewGStyleData->GetColor().A);
                    m_isFaceColorOverride[categoryIndex] = opViewGStyleOverriddenItems->IsColorOverridden();

                    OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                    if (!CanCategoryHaveGraphicsMaterial(pUIDoc->GetDbDocument(), categoryUid))
                    {
                        overrideItem->setFlags(overrideItem->flags() & (~Qt::ItemIsEnabled));
                        overrideItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
                    }
                    else
                    {
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        if (!m_isFaceColorOverride[categoryIndex])
                        {
                            overrideItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                        }
                        else
                        {
                            overrideItem->setText(QString::fromStdWString(GBMP_TR(L"")));
                            overrideItem->setBackgroundColor(m_faceColor[categoryIndex]);
                        }
                    }
                    m_table->setItem(categoryIndex, 5, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                }

                // 截面颜色
                {
                    m_sectionFaceColor[categoryIndex] = QColor(opViewGStyleData->GetSectionFaceColor().R, opViewGStyleData->GetSectionFaceColor().G, opViewGStyleData->GetSectionFaceColor().B, opViewGStyleData->GetSectionFaceColor().A);
                    m_isSectionFaceColorOverride[categoryIndex] = opViewGStyleOverriddenItems->IsSectionFaceColorOverridden();

                    OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                    if (!CanCategoryHaveGraphicsMaterial(pUIDoc->GetDbDocument(), categoryUid))
                    {
                        overrideItem->setFlags(overrideItem->flags() & (~Qt::ItemIsEnabled));
                        overrideItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
                    }
                    else
                    {
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        if (!m_isSectionFaceColorOverride[categoryIndex])
                        {
                            overrideItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                        }
                        else
                        {
                            overrideItem->setText(QString::fromStdWString(GBMP_TR(L"")));
                            overrideItem->setBackgroundColor(m_sectionFaceColor[categoryIndex]);
                        }
                    }
                    m_table->setItem(categoryIndex, 6, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                }

                // 面填充
                m_faceHatchData[categoryIndex].GetDataFrom(*pDocGStyle, pModelView);
                for (int iFaceType = 0; iFaceType <= (int)HatchFaceType::Section; ++iFaceType)
                {
                    std::wstring currHatchPattern;
                    bool isCurrHatchPatternOverride;
                    int column;

                    if (iFaceType == (int)HatchFaceType::Projection)
                    {
                        currHatchPattern = m_faceHatchData[categoryIndex].m_projectionPattern;
                        isCurrHatchPatternOverride = m_faceHatchData[categoryIndex].m_isProjectionPatternOverride;
                        column = 7;
                    }
                    else if (iFaceType == (int)HatchFaceType::Section)
                    {
                        currHatchPattern = m_faceHatchData[categoryIndex].m_sectionPattern;
                        isCurrHatchPatternOverride = m_faceHatchData[categoryIndex].m_isSectionPatternOverride;
                        column = 8;
                    }
                    else
                    {
                        DBG_WARN(L"无法识别的面类型",L"GDMPLab",L"2024-03-30");
                        continue;
                    }

                    if (!CanCategoryHaveGraphicsMaterial(pUIDoc->GetDbDocument(), categoryUid))
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setFlags(overrideItem->flags() & (~Qt::ItemIsEnabled));
                        overrideItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
                        m_table->setItem(categoryIndex, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                    else
                    {
                        gcmp::OwnerPtr<QComboBox> comBox = NEW_AS_OWNER_PTR(QComboBox);

                        IDocument* pDoc = m_wpUIDoc->GetDbDocument();
                        DBG_WARN_AND_CONTINUE_UNLESS(pDoc, L"pDoc为空",L"GDMPLab",L"2024-03-30");
                        std::vector<std::wstring> hatchPatternNames;
                        std::vector<IElement*> HatchPatternElements = pDoc->GetElementsByCategory(BuiltInCategoryUniIdentities::BICU_HATCH_PATTERN);
                        FOR_EACH(pHatchPatternElement, HatchPatternElements)
                        {
                            IGenericElement* pHatchPatternEle = quick_cast<IGenericElement>(pHatchPatternElement);
                            if (!pHatchPatternEle)
                            {
                                continue;
                            }

                            IExternalObject* pExternalObject = pHatchPatternEle->GetExternalObject();
                            DBG_WARN_AND_CONTINUE_UNLESS(pExternalObject, L"pExternalObject为空",L"GDMPLab",L"2024-03-30");
                            IFillPattern* pHatchPatternElement = quick_cast<IFillPattern>(pExternalObject);
                            DBG_WARN_AND_CONTINUE_UNLESS(pHatchPatternElement, L"pHatchPatternElement为空",L"GDMPLab",L"2024-03-30");
                            const IFillPatternData* pHatchPattern = pHatchPatternElement->GetFillPatternData();
                            DBG_WARN_AND_CONTINUE_UNLESS(pHatchPattern, L"pHatchPattern1为空",L"GDMPLab",L"2024-03-30");
                            std::wstring name = pHatchPattern->GetName();
                            hatchPatternNames.push_back(name);
                        }
                        int nPattern = static_cast<int>(hatchPatternNames.size());
                        if (nPattern <= 0)
                        {
                            DBG_WARN(L"没有面填充图案？",L"GDMPLab",L"2024-03-30");
                            comBox->setEnabled(false);
                        }
                        else
                        {
                            int iCurrPattern = -1;

                            QStringList hatchPatternsList;
                            hatchPatternsList << STR(GBMP_TR(L"无替换"));
                            for (int iPattern = 0; iPattern < nPattern; ++iPattern)
                            {
                                std::wstring patternString = hatchPatternNames[iPattern];
                                hatchPatternsList << STR(GBMP_TR(patternString));
                                if (patternString == currHatchPattern)
                                    iCurrPattern = iPattern + 1;
                            }
                            comBox->addItems(hatchPatternsList);

                            if (!isCurrHatchPatternOverride)
                            {
                                comBox->setCurrentIndex(0);
                            }
                            else if (iCurrPattern == -1)
                            {
                                comBox->setCurrentIndex(1);
                            }
                            else
                            {
                                comBox->setCurrentIndex(iCurrPattern);
                            }
                        }
                        m_table->setCellWidget(categoryIndex, column, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(comBox));
                    }
                }

                //面透明度
                {
                    if (!CanCategoryHaveGraphicsMaterial(pUIDoc->GetDbDocument(), categoryUid))
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setFlags(overrideItem->flags() & (~Qt::ItemIsEnabled));
                        overrideItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
                        m_table->setItem(categoryIndex, 8, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                    else
                    {
                        gcmp::OwnerPtr<QLineEdit> lineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                        QIntValidator* pInputValidator = NEW_AS_QT_CHILD(QIntValidator, this);
                        pInputValidator->setRange(0, 255);
                        lineEdit->setValidator(pInputValidator);
                        if (!opViewGStyleOverriddenItems->IsTransparencyOverridden())
                        {
                            int idx = static_cast<unsigned char>(opDocGStyleData->GetTransparency() * 255.0 + 0.5);
                            lineEdit->setText(QString::number(idx));
                        }
                        else
                        {
                            int idx = static_cast<unsigned char>(opViewGStyleData->GetTransparency() * 255.0 + 0.5);
                            lineEdit->setText(QString::number(idx));
                        }

                        m_table->setCellWidget(categoryIndex, 9, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(lineEdit));
                    }
                }

                // 详细程度
                {
                    QStringList detailLevelList;
                    detailLevelList << STR(GBMP_TR(L"按视图")) << STR(GBMP_TR(L"粗略")) << STR(GBMP_TR(L"中等")) << STR(GBMP_TR(L"精细"));

                    gcmp::OwnerPtr<QComboBox> comBox = NEW_AS_OWNER_PTR(QComboBox);
                    comBox->addItems(detailLevelList);

                    if (!pModelView->HasOverrideDetailLevel(categoryUid))
                    {
                        comBox->setCurrentIndex(0);
                    }
                    else
                    {
                        int idx = (int)pModelView->GetDetailLevelOverrideByCategoryUid(categoryUid) + 1;
                        comBox->setCurrentIndex(idx);
                    }

                    m_table->setCellWidget(categoryIndex, 10, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(comBox));
                }

                // 投影面颜色
                {
                    m_faceHatchData[categoryIndex].m_projectionColor = opViewGStyleData->GetProjectionFaceHatchColor();

                    OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                    {
                        overrideItem->setFlags(overrideItem->flags() & (~Qt::ItemIsEnabled));
                        overrideItem->setBackgroundColor(QColor(m_faceHatchData[categoryIndex].m_projectionColor.R, m_faceHatchData[categoryIndex].m_projectionColor.G, m_faceHatchData[categoryIndex].m_projectionColor.B, m_faceHatchData[categoryIndex].m_projectionColor.A));
                    }
                    m_table->setItem(categoryIndex, 11, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                }
                
                // 上投影线
                {
                    std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewForegroundGStyleTmp =
                        pModelView->GetForegroundGraphicsStyleOverrideByCategoryUid(categoryUid);
                    OwnerPtr<IGraphicsStyleDataOverrideItems>& opViewForegroundGStyleOverriddenItems = opCurViewForegroundGStyleTmp.first;
                    OwnerPtr<IGraphicsStyleData>& opViewForegroundGStyleData = opCurViewForegroundGStyleTmp.second;
                    bool isProjectOverride = false;
                    isProjectOverride = opViewForegroundGStyleOverriddenItems->IsProjectionLineColorOverridden() || opViewForegroundGStyleOverriddenItems->IsProjectionLineWidthOverridden()
                        || opViewForegroundGStyleOverriddenItems->IsProjectionLineTypeNameOverridden();

                    LineStyle lineSyle;
                    if (opViewForegroundGStyleOverriddenItems->IsProjectionLineWidthOverridden())
                    {
                        lineSyle.lineWidth = opViewForegroundGStyleData->GetProjectionLineWidth();
                    }
                    else
                    {
                        lineSyle.lineWidth = -1.0;
                        lineSyle.oldLineWidth = opViewForegroundGStyleData->GetProjectionLineWidth();
                    }

                    if (opViewForegroundGStyleOverriddenItems->IsProjectionLineColorOverridden())
                    {
                        lineSyle.lineColor = opViewForegroundGStyleData->GetProjectionLineColor();
                        lineSyle.hasColorOverride = true;
                    }
                    else
                    {
                        lineSyle.hasColorOverride = false;
                        lineSyle.oldLineColor = opViewForegroundGStyleData->GetProjectionLineColor();
                    }

                    if (opViewForegroundGStyleOverriddenItems->IsProjectionLineTypeNameOverridden())
                    {
                        lineSyle.lineType = opViewForegroundGStyleData->GetProjectionLineTypeName();
                    }
                    else
                    {
                        lineSyle.lineType = std::wstring(GBMP_TR(L"无替换"));
                        lineSyle.oldLineType = opViewForegroundGStyleData->GetProjectionLineTypeName();
                    }

                    int cellIndex = buttonOffset * 12 + categoryIndex;
                    m_cellIndexLineStyleMap[cellIndex] = lineSyle;

                    if (!isProjectOverride)
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 12, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                    else
                    {
                        OwnerPtr<QTableWidgetItem> overrideItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        overrideItem->setText(QString::fromStdWString(GBMP_TR(L"")));
                        overrideItem->setFlags(Qt::ItemIsEnabled);
                        m_table->setItem(categoryIndex, 12, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(overrideItem));
                    }
                }

            }
            else//文档样式初始化
            {
                // 样式名称
                {
                    gcmp::OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem, QString::fromStdWString(categoryName));
                    opItem->setFlags(Qt::ItemIsEnabled);
                    m_table->setItem(categoryIndex, 0, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
                }

                //添加投影线宽
                {
                    double lineWidth = opDocGStyleData->GetProjectionLineWidth();

                    if (IsViewGraphicsStyle())
                    {
                        // 视图样式使用下拉菜单，保持"不重载"选项
                        QStringList widthList;
                        widthList << STR(GBMP_TR(L"不重载"));
                        for (int ii = 1; ii <= 40; ++ii)
                        {
                            widthList << QString::number(ii, 10);
                        }
                        if (lineWidth > 40)
                        {
                            widthList << QString::number(lineWidth, 'f', 2);
                        }

                        gcmp::OwnerPtr<QComboBox> comBox = NEW_AS_OWNER_PTR(QComboBox);
                        comBox->addItems(widthList);
                        if (lineWidth <= 40)
                        {
                            comBox->setCurrentIndex((int)lineWidth);
                        }
                        else
                        {
                            comBox->setCurrentIndex(41);
                        }
                        m_table->setCellWidget(categoryIndex, 1, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(comBox));
                    }
                    else
                    {
                        // 文档样式使用直接输入
                        gcmp::OwnerPtr<QLineEdit> lineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                        QDoubleValidator* validator = NEW_AS_QT_CHILD(QDoubleValidator, this);
                        validator->setRange(0.0001, 100000000);
                        validator->setDecimals(2);
                        lineEdit->setValidator(validator);
                        lineEdit->setText(QString::number(lineWidth));
                        m_table->setCellWidget(categoryIndex, 1, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(lineEdit));
                    }
                }

                //添加投影颜色
                {
                    gcmp::OwnerPtr<QPushButton> colorShowing = NEW_AS_OWNER_PTR(QPushButton);
                    const Color& color = opDocGStyleData->GetProjectionLineColor();

                    //看是否数据是非重载
                    {
                        QColor qColor(color.R, color.G, color.B, color.A);

                        //存到color map里.
                        m_colorMap[CellType(categoryIndex, 2)] = qColor;

                        colorShowing->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
                    }
                    m_opColorBtnGroup->addButton(colorShowing.get(), categoryIndex);
                    m_table->setCellWidget(categoryIndex, 2, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(colorShowing));
                }

                //投影线型
                {
                    // AddVisibilityCtrl(visibilityGraphicsPtrArray[i], categoryUid, categoryIndex, 3);
                    gcmp::OwnerPtr<QPushButton> pLineTypeButton = NEW_AS_OWNER_PTR(QPushButton);
                    pLineTypeButton->setText(QString::fromStdWString(opDocGStyleData->GetProjectionLineTypeName()));
                    m_opLineTypeBtnGroup->addButton(pLineTypeButton.get(), categoryIndex);
                    m_table->setCellWidget(categoryIndex, 3, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pLineTypeButton));
                }

                // 截面线宽。
                {
                    double lineWidth = opDocGStyleData->GetSectionLineWidth();

                    if (IsViewGraphicsStyle())
                    {
                        // 视图样式使用下拉菜单，保持"不重载"选项
                        QStringList widthList;
                        widthList << STR(GBMP_TR(L"不重载"));
                        for (int ii = 1; ii <= 40; ++ii)
                        {
                            widthList << QString::number(ii, 10);
                        }
                        if (lineWidth > 40)
                        {
                            widthList << QString::number(lineWidth, 'f', 2);
                        }

                        gcmp::OwnerPtr<QComboBox> comBox = NEW_AS_OWNER_PTR(QComboBox);
                        comBox->addItems(widthList);
                        if (lineWidth <= 40)
                        {
                            comBox->setCurrentIndex((int)lineWidth);
                        }
                        else
                        {
                            comBox->setCurrentIndex(41);
                        }
                        m_table->setCellWidget(categoryIndex, 4, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(comBox));
                    }
                    else
                    {
                        // 文档样式使用直接输入
                        gcmp::OwnerPtr<QLineEdit> lineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                        QDoubleValidator* validator = NEW_AS_QT_CHILD(QDoubleValidator, this);
                        validator->setRange(0.0001, 100000000);
                        validator->setDecimals(2);
                        lineEdit->setValidator(validator);
                        lineEdit->setText(QString::number(lineWidth));
                        m_table->setCellWidget(categoryIndex, 4, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(lineEdit));
                    }
                }

                //添加截面线颜色
                {
                    gcmp::OwnerPtr<QPushButton> colorShowing = NEW_AS_OWNER_PTR(QPushButton);
                    const Color& color = opDocGStyleData->GetSectionLineColor();

                    //看是否数据是非重载                   
                    {
                        QColor qColor(color.R, color.G, color.B, color.A);

                        //存到color map里.
                        m_colorMap[CellType(categoryIndex + buttonOffset, 2)] = qColor;

                        colorShowing->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
                    }
                    m_opColorBtnGroup->addButton(colorShowing.get(), categoryIndex + buttonOffset);
                    m_table->setCellWidget(categoryIndex, 5, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(colorShowing));
                }

                // 截面线型
                {
                    gcmp::OwnerPtr<QPushButton> pLineTypeButton = NEW_AS_OWNER_PTR(QPushButton);
                    pLineTypeButton->setText(QString::fromStdWString(opDocGStyleData->GetSectionLineTypeName()));
                    m_opLineTypeBtnGroup->addButton(pLineTypeButton.get(), categoryIndex + buttonOffset);
                    m_table->setCellWidget(categoryIndex, 6, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pLineTypeButton));
                }

                // 隐藏线宽。
                {
                    double lineWidth = opDocGStyleData->GetHiddenLineWidth();

                    if (IsViewGraphicsStyle())
                    {
                        // 视图样式使用下拉菜单，保持"不重载"选项
                        QStringList widthList;
                        widthList << STR(GBMP_TR(L"不重载"));
                        for (int ii = 1; ii <= 40; ++ii)
                        {
                            widthList << QString::number(ii, 10);
                        }
                        if (lineWidth > 40)
                        {
                            widthList << QString::number(lineWidth, 'f', 2);
                        }

                        gcmp::OwnerPtr<QComboBox> comBox = NEW_AS_OWNER_PTR(QComboBox);
                        comBox->addItems(widthList);
                        if (lineWidth <= 40)
                        {
                            comBox->setCurrentIndex((int)lineWidth);
                        }
                        else
                        {
                            comBox->setCurrentIndex(41);
                        }
                        m_table->setCellWidget(categoryIndex, 7, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(comBox));
                    }
                    else
                    {
                        // 文档样式使用直接输入
                        gcmp::OwnerPtr<QLineEdit> lineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                        QDoubleValidator* validator = NEW_AS_QT_CHILD(QDoubleValidator, this);
                        validator->setRange(0.0001, 100000000);
                        validator->setDecimals(2);
                        lineEdit->setValidator(validator);
                        lineEdit->setText(QString::number(lineWidth));
                        m_table->setCellWidget(categoryIndex, 7, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(lineEdit));
                    }
                }

                //添加隐藏线颜色
                {
                    gcmp::OwnerPtr<QPushButton> colorShowing = NEW_AS_OWNER_PTR(QPushButton);
                    const Color& color = opDocGStyleData->GetHiddenLineColor();

                    //看是否数据是非重载                   
                    {
                        QColor qColor(color.R, color.G, color.B, color.A);

                        //存到color map里.
                        m_colorMap[CellType(categoryIndex + buttonOffset * 2, 2)] = qColor;

                        colorShowing->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
                    }
                    m_opColorBtnGroup->addButton(colorShowing.get(), categoryIndex + buttonOffset * 2);
                    m_table->setCellWidget(categoryIndex, 8, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(colorShowing));
                }

                // 隐藏线型
                {
                    gcmp::OwnerPtr<QPushButton> pLineTypeButton = NEW_AS_OWNER_PTR(QPushButton);
                    pLineTypeButton->setText(QString::fromStdWString(opDocGStyleData->GetHiddenLineTypeName()));
                    m_opLineTypeBtnGroup->addButton(pLineTypeButton.get(), categoryIndex + buttonOffset * 2);
                    m_table->setCellWidget(categoryIndex, 9, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pLineTypeButton));
                }

                // 材质
                {
                    InitMaterialCell(pDoc, categoryIndex, categoryUid, pDocGStyle);
                }

                // 面样式
                {
                    if (CanCategoryHaveGraphicsMaterial(pUIDoc->GetDbDocument(), categoryUid))
                    {
                        gcmp::OwnerPtr<QPushButton> colorShowing = NEW_AS_OWNER_PTR(QPushButton);
                        const Color& color = opDocGStyleData->GetColor();
                        QColor qColor(color.R, color.G, color.B, color.A);
                        colorShowing->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
                        m_buttonRowMap[(WndId)colorShowing->winId()] = categoryIndex;
                        m_opFaceStyleBtnGroup->addButton(colorShowing.get(), categoryIndex + buttonOffset);
                        m_table->setCellWidget(categoryIndex, 11, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(colorShowing));
                    }
                    else
                    {
                        OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
                        opItem->setFlags(opItem->flags() & (~Qt::ItemIsEnabled));
                        opItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
                        m_table->setItem(categoryIndex, 11, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
                    }
                }
                // 透明度
                {
                    gcmp::OwnerPtr<QLineEdit> lineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                    QIntValidator* pInputValidator = NEW_AS_QT_CHILD(QIntValidator, this);
                    pInputValidator->setRange(0, 255);
                    lineEdit->setValidator(pInputValidator);
                    int idx = static_cast<unsigned char>(opDocGStyleData->GetTransparency() * 255.0 + 0.5);
                    lineEdit->setText(QString::number(idx));
                    m_table->setCellWidget(categoryIndex, 12, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(lineEdit));
                }
                // 投影线alpha是否有效
                {
                    m_table->horizontalHeader()->setSectionResizeMode(0, QHeaderView::ResizeToContents);
                    bool enable = opDocGStyleData->IsProjectionLineColorAlphaEnabled();
                    gcmp::OwnerPtr<QCheckBox> pCheckBox(NEW_AS_OWNER_PTR(QCheckBox));
                    pCheckBox->setChecked(enable);
                    m_table->setCellWidget(categoryIndex, 13, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pCheckBox));
                }
                // 上投影线
                {
                    ElementId foregroundGsID = pGStyleManager->GetForegroundGraphicsStyleIdByCategoryUid(categoryUid);
                    OwnerPtr<IGraphicsStyleData> opDocForegroundGStyleData = nullptr;
                    if (foregroundGsID.IsValid())
                    {
                        IGraphicsStyle* pDocForegroundGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(foregroundGsID));
                        DBG_WARN_AND_CONTINUE_UNLESS(pDocForegroundGStyle, L"IGraphicsStyle is null",L"GDMPLab",L"2024-03-30");
                        opDocForegroundGStyleData = pDocForegroundGStyle->GetGraphicsStyleData();
                    }
                    else
                    {
                        opDocForegroundGStyleData = pDocGStyle->GetGraphicsStyleData();
                    }
                    DBG_WARN_AND_CONTINUE_UNLESS(opDocForegroundGStyleData, L"opDocForegroundGStyleData is null",L"GDMPLab",L"2024-03-30");
                    //线宽
                    {
                        double lineWidth = opDocForegroundGStyleData->GetProjectionLineWidth();

                        if (IsViewGraphicsStyle())
                        {
                            // 视图样式使用下拉菜单，保持"不重载"选项
                            QStringList widthList;
                            widthList << STR(GBMP_TR(L"不重载"));
                            for (int ii = 1; ii <= 40; ++ii)
                            {
                                widthList << QString::number(ii, 10);
                            }
                            if (lineWidth > 40)
                            {
                                widthList << QString::number(lineWidth, 'f', 2);
                            }

                            gcmp::OwnerPtr<QComboBox> comBox = NEW_AS_OWNER_PTR(QComboBox);
                            comBox->addItems(widthList);
                            if (lineWidth <= 40)
                            {
                                comBox->setCurrentIndex((int)lineWidth);
                            }
                            else
                            {
                                comBox->setCurrentIndex(41);
                            }
                            m_table->setCellWidget(categoryIndex, 14, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(comBox));
                        }
                        else
                        {
                            // 文档样式使用直接输入
                            gcmp::OwnerPtr<QLineEdit> lineEdit = NEW_AS_OWNER_PTR(QLineEdit);
                            QDoubleValidator* validator = NEW_AS_QT_CHILD(QDoubleValidator, this);
                            validator->setRange(0.0001, 100000000);
                            validator->setDecimals(2);
                            lineEdit->setValidator(validator);
                            lineEdit->setText(QString::number(lineWidth));
                            m_table->setCellWidget(categoryIndex, 14, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(lineEdit));
                        }
                    }
                    //上投影线颜色
                    {
                        gcmp::OwnerPtr<QPushButton> colorShowing = NEW_AS_OWNER_PTR(QPushButton);
                        const Color& color = opDocForegroundGStyleData->GetProjectionLineColor();

                        //看是否数据是非重载
                        {
                            QColor qColor(color.R, color.G, color.B, color.A);

                            //存到color map里.
                            m_colorMap[CellType(categoryIndex + buttonOffset * 3, 2)] = qColor;

                            colorShowing->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
                        }
                        m_opColorBtnGroup->addButton(colorShowing.get(), categoryIndex + buttonOffset * 3);
                        m_table->setCellWidget(categoryIndex, 15, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(colorShowing));
                    }

                    //线型
                    {
                        gcmp::OwnerPtr<QPushButton> pLineTypeButton = NEW_AS_OWNER_PTR(QPushButton);
                        pLineTypeButton->setText(QString::fromStdWString(opDocForegroundGStyleData->GetProjectionLineTypeName()));
                        m_opLineTypeBtnGroup->addButton(pLineTypeButton.get(), categoryIndex + buttonOffset * 3);
                        m_table->setCellWidget(categoryIndex, 16, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pLineTypeButton));
                    }
                }
            }
        }
    }

    // update row count at last in case some categories are filtered out.
    m_table->setRowCount(categoryIndex + 1);
}

void GraphicsStyleTable::InitMaterialCell(const gcmp::IDocument* pDoc, int categoryIndex, const UniIdentity& categoryUid, const gcmp::IGraphicsStyle* pGStyleElement)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"pDoc is null",L"GDMPLab",L"2024-03-30");

    if (CanCategoryHaveGraphicsMaterial(m_wpUIDoc->GetDbDocument(), categoryUid))
    {
        // 计算出材质下拉列表中可见的内容
        std::vector<std::wstring> itemNames = CalcItemNames_inMaterialComboBox();

        std::wstring materialName = GBMP_TR(L"无");
        ElementId materialId = pDoc->GetGraphicsMaterialManager()->GetGraphicsMaterialIdByCategoryUid(categoryUid);
        if (materialId.IsValid())
        {
            m_materialIds[categoryIndex] = materialId;

            IElement* pElement = m_wpUIDoc->GetDbDocument()->GetElement(materialId);
            DBG_WARN_AND_RETURN_VOID_UNLESS(pElement != nullptr, L"pElement为空",L"GDMPLab",L"2024-03-30");
            IGraphicsMaterial* pMaterialElement = quick_cast<IGraphicsMaterial>(pElement);
            DBG_WARN_AND_RETURN_VOID_UNLESS(pMaterialElement != nullptr, L"打开 IGraphicsMaterial 对象失败",L"GDMPLab",L"2024-03-30");

            // 当前类别所关联的材质的名称
            if (pMaterialElement->IsBoundWithMaterialInstance())
            {
                OwnerPtr<IGraphicsMaterialData> pMaterialData = pMaterialElement->GetGraphicsMaterialDataFromMaterialInstance();
                if (pMaterialData)
                {
                    pMaterialElement->GetMaterialInstanceName(materialName);
                }
                else
                {
                    materialName = GBMP_TR(L"<加载失败>");
                }
            }
        }

        // 计算下拉列表的当前项
        QStringList itemNames_4_qt;
        int currentComboBoxIndex = 0, iIndex = 0;
        FOR_EACH(itemName, itemNames)
        {
            itemNames_4_qt.push_back(QString::fromStdWString(itemName));
            if (itemName == materialName)
            {
                currentComboBoxIndex = iIndex;
            }

            iIndex++;
        }

        // 创建下拉列表
        gcmp::OwnerPtr<QComboBox> pComboBox = NEW_AS_OWNER_PTR(QComboBox);
        pComboBox->addItems(itemNames_4_qt);
        pComboBox->setCurrentIndex(currentComboBoxIndex);
        pComboBox->setProperty("row", categoryIndex);
        pComboBox->setProperty("col", 10);
        connect(pComboBox.get(), SIGNAL(currentIndexChanged(int)), this, SLOT(OnMaterialComboBox_currentIndexChanged(int)));
        m_materialComboBoxList.push_back(pComboBox.get());
        m_table->setCellWidget(categoryIndex, 10, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pComboBox));
    }
    else
    {
        OwnerPtr<QTableWidgetItem> opItem = NEW_AS_OWNER_PTR(QTableWidgetItem);
        opItem->setFlags(opItem->flags() & (~Qt::ItemIsEnabled));
        opItem->setBackgroundColor(QColor(Color::Silver.R, Color::Silver.G, Color::Silver.B, Color::Silver.A));
        m_table->setItem(categoryIndex, 11, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(opItem));
    }

    // 面颜色
    OwnerPtr<IGraphicsStyleData> opStyleData = pGStyleElement->GetGraphicsStyleData();
    DBG_WARN_AND_RETURN_VOID_UNLESS(opStyleData, L"pGStyleElement->GetGraphicsStyleData() 返回空指针",L"GDMPLab",L"2024-03-30");
    m_faceColor[categoryIndex] = QColor(opStyleData->GetColor().R, opStyleData->GetColor().G, opStyleData->GetColor().B, opStyleData->GetColor().A);
    m_sectionFaceColor[categoryIndex] = QColor(opStyleData->GetSectionFaceColor().R, opStyleData->GetSectionFaceColor().G, opStyleData->GetSectionFaceColor().B, opStyleData->GetSectionFaceColor().A);

    // huanglw:文档级样式，不需要重载
    /*const IGraphicsStyleInternal* pGraphicsStyleInternal = dynamic_cast<const IGraphicsStyleInternal*>(pGStyleElement);
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGraphicsStyleInternal, L"dynamic_cast<const IGraphicsStyleInternal*>(pGStyleElement) 返回空指针",L"GDMPLab",L"2024-03-30");
    m_isFaceColorOverride[categoryIndex] = pGraphicsStyleInternal->IsColorOverrided();   // 这里如何处理？文档级使用视图级的数据？
    */
    // 面填充
    m_faceHatchData[categoryIndex].GetDataFrom(*pGStyleElement);
}

void GraphicsStyleTable::OnClickCell(int bnt)
{
    //打开辅助对话框

    QPushButton *colorButton = dynamic_cast<QPushButton*>(m_opColorBtnGroup->button(bnt));
    int row = bnt;

    QColor qColor = m_colorMap[CellType(row, 2)];
    QColor resColor;
    bool noOverride = false;
    if (!IsViewGraphicsStyle())
    {
        //文档模式下直接打开颜色对话框获取颜色
        QColorDialog* colorwidget = NEW_AS_QT_CHILD(QColorDialog, m_parent);
        colorwidget->setCurrentColor(qColor);
        resColor = colorwidget->getColor(qColor, m_parent, QString::fromStdWString(GBMP_TR(L"选择颜色")));
        if (resColor.isValid())
        {
            colorButton->setStyleSheet(QString("background-color:%1").arg(resColor.name()));
        }
        else
        {
            colorButton->setStyleSheet(QString("background-color:%1").arg(qColor.name()));
        }
        //删除默认文字
        if (colorButton->text() == STR(L"Default"))
        {
            colorButton->setText(QString::Null());
        }
    }
    else
    {
        //视图模式下，先打开一个对话框
        SelectGColorDlg *selectGColorDlg = NEW_AS_QT_CHILD(SelectGColorDlg, qColor, colorButton, m_parent);
        selectGColorDlg->exec();
        if (selectGColorDlg->IsNoOverride())
        {
            //点了不重载，要删除相应颜色
            m_colorMap.erase(CellType(row, 2));
            noOverride = true;
        }
        else
        {
            //选择好了颜色
            resColor = selectGColorDlg->GetResultColor();
        }
    }
    if (!noOverride)
    {
        m_colorMap[CellType(row, 2)] = resColor.isValid() ? resColor : qColor;
    }
}

void GraphicsStyleTable::RecordMaterialDatas()
{
    if (m_wpUIDoc.Get() == nullptr)
    {
        return;
    }

    gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-03-30");

    std::set<UniIdentity> allVisibleCategories = GraphicsStyleDlg::GetAllCategoriesWithGStyles(pDoc);
    IGraphicsStyleManager* pGStyleManager = pDoc->GetGraphicsStyleManager();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGStyleManager, L"pGStyleManager 为空",L"GDMPLab",L"2024-03-30");

    int categoryIndex = -1;
    FOR_EACH(categoryUid, m_categories) // 这一页准备显示的类别
    {
        // 看看文档中是否有这个类别
        bool bExistInDoc = std::find(allVisibleCategories.begin(), allVisibleCategories.end(), categoryUid) != allVisibleCategories.end();
        // 保存这个类别
        if (bExistInDoc)
        {
            ++categoryIndex;

            ElementId syleId = pGStyleManager->GetGraphicsStyleIdByCategoryUid(categoryUid);
            IGraphicsStyle* pDocGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(syleId));

            // 处理重载视图样式
            if (IsViewGraphicsStyle())
            {
                IModelView *pModelView = GetModelView();
                DBG_WARN_AND_CONTINUE_UNLESS(pModelView, L"dynamic_cast<ModelView*>(GetModelView()) 返回空指针",L"GDMPLab",L"2024-03-30");

                // 如果目标类别没有重载样式，则重载(样式数据使用文档级别的样式数据)。如果已有重载，则不作任何事
                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opGStyleOverriddenItems = pModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                pModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opGStyleOverriddenItems.first, *opGStyleOverriddenItems.second);

                // todo: 因为现在视图对象样式中已经不能整个重载材质了，因此此处需要删除
                //日志回放时，已在CmdViewMaterialSet中做了处理
                if (!gcmp::JournalUtils::IsInReplay())
                {
                    UpdateMaterialChanges_to_doc();
                    RecordMaterialCommand(ID_CMD_VIEW_MATERIAL_SET, m_materialIds[categoryIndex], categoryUid);
                }
            }
            else
            {
                //日志回放时，已在CmdProjectMaterialSet中做了处理
                if (!gcmp::JournalUtils::IsInReplay())
                {
                    UpdateMaterialChanges_to_doc();
                    RecordMaterialCommand(ID_CMD_PROJECT_MATERIAL_SET, m_materialIds[categoryIndex], categoryUid);
                }
            }
        }
    }
}

void GraphicsStyleTable::OnClickCell_faceStyle(int bnt)
{
    QPushButton* pFaceStyleButton = dynamic_cast<QPushButton*>(m_opFaceStyleBtnGroup->button(bnt));
    int row = m_buttonRowMap[(WndId)pFaceStyleButton->winId()];

    IDocument * pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc != nullptr, L"m_wpUIDoc->GetDbDocument() 返回空指针",L"GDMPLab",L"2024-03-30");

    const UniIdentity& categoryUid = m_categories[row];
    if (!CanCategoryHaveGraphicsMaterial(pDoc, categoryUid))
        return;

    Color initialColor(m_faceColor[row].red(), m_faceColor[row].green(), m_faceColor[row].blue(), m_faceColor[row].alpha());
    Color initialSectionFaceColor(m_sectionFaceColor[row].red(), m_sectionFaceColor[row].green(), m_sectionFaceColor[row].blue(), m_sectionFaceColor[row].alpha());
    FaceStyleSettingDialog settingDialog(pDoc, categoryUid, initialColor, initialSectionFaceColor, m_faceHatchData[row].m_projectionColor,
        m_faceHatchData[row].m_projectionPattern, m_faceHatchData[row].m_projectionAngle, m_faceHatchData[row].m_projectionScale, m_faceHatchData[row].m_sectionColor, m_faceHatchData[row].m_sectionPattern, m_faceHatchData[row].m_sectionAngle, m_faceHatchData[row].m_sectionScale);
    if (settingDialog.exec() != QDialog::Accepted)
        return;

    m_faceColor[row] = QColor(settingDialog.GetFaceColor().R, settingDialog.GetFaceColor().G, settingDialog.GetFaceColor().B, settingDialog.GetFaceColor().A);
    m_sectionFaceColor[row] = QColor(settingDialog.GetSectionFaceColor().R, settingDialog.GetSectionFaceColor().G, settingDialog.GetSectionFaceColor().B, settingDialog.GetSectionFaceColor().A);
    m_faceHatchData[row].m_projectionColor = settingDialog.GetProjectionFaceHatchColor();
    m_faceHatchData[row].m_projectionPattern = settingDialog.GetProjectionFaceHatchPattern();
    m_faceHatchData[row].m_sectionColor = settingDialog.GetSectionFaceHatchColor();
    m_faceHatchData[row].m_sectionPattern = settingDialog.GetSectionFaceHatchPattern();
    m_faceHatchData[row].m_projectionAngle = settingDialog.GetProjectionFaceHatchAngle();
    m_faceHatchData[row].m_projectionScale = settingDialog.GetProjectionFaceHatchScale();
    m_faceHatchData[row].m_sectionAngle = settingDialog.GetSectionFaceHatchAngle();
    m_faceHatchData[row].m_sectionScale = settingDialog.GetSectionFaceHatchScale();


    // 设置按钮颜色
    pFaceStyleButton->setStyleSheet(QString("background-color:%1").arg(m_faceColor[row].name()));
}

void GraphicsStyleTable::OnClickCell(int row, int column)
{
    gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"pDoc 返回空指针",L"GDMPLab",L"2024-03-30");

    if (IsViewGraphicsStyle())
    {
        if (column == 2 || column == 3 || column == 4 || column == 12)
        {
            int cellIndex = column*buttonOffset + row;
            auto it = m_cellIndexLineStyleMap.find(cellIndex);
            LineStyle lineStyle;
            if (it != m_cellIndexLineStyleMap.end())
            {
                lineStyle = it->second;
            }
            LineStyleDlg dlg;
            dlg.SetLineColor(lineStyle.lineColor);
            dlg.SetLineColorOverride(lineStyle.hasColorOverride);
            dlg.SetLineWidth(lineStyle.lineWidth);
            dlg.SetLineType(lineStyle.lineType);
            dlg.Init();
            if (dlg.exec() == (int)QDialog::Accepted)
            {
                QTableWidgetItem *pItem = m_table->item(row, column);
                DBG_WARN_AND_RETURN_VOID_UNLESS(pItem, L"找不到 QTableWidgetItem 对象",L"GDMPLab",L"2024-03-30");
                double lineWidth = dlg.GetLineWidth();
                bool hasLineWidthOverride = false;
                if (lineWidth != -1)
                {
                    hasLineWidthOverride = true;
                }
                bool hasLineColorOverride = dlg.GetLineColorOverride();
                bool hasLineTypeOverride = false;
                if (dlg.GetLineType() != GBMP_TR(L"无替换"))
                {
                    hasLineTypeOverride = true;
                }

                if (hasLineColorOverride || hasLineTypeOverride || hasLineWidthOverride)
                {
                    pItem->setText(QString::fromStdWString(L""));
                }
                else
                {
                    pItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                }

                LineStyle reslineStyle = lineStyle;
                reslineStyle.lineColor = dlg.GetLineColor();
                reslineStyle.lineWidth = dlg.GetLineWidth();
                reslineStyle.lineType = dlg.GetLineType();
                reslineStyle.hasColorOverride = dlg.GetLineColorOverride();
                if (it != m_cellIndexLineStyleMap.end())
                {
                    it->second = reslineStyle;
                }
            }
        }
        else if (column == 5) // 投影面颜色
        {
            if (!CanCategoryHaveGraphicsMaterial(pDoc, m_categories[row]))
                return;

            ColorSettingInquiryDialog inquiryDialog(m_faceColor[row], m_parent);
            if (inquiryDialog.exec() != QDialog::Accepted)
                return;

            QTableWidgetItem *pItem = m_table->item(row, column);
            DBG_WARN_UNLESS(pItem != nullptr, L"找不到 QTableWidgetItem 对象",L"GDMPLab",L"2024-03-30");

            if (!inquiryDialog.IsToOverrideColor())
            {
                m_isFaceColorOverride[row] = false;
                if (pItem)
                {
                    pItem->setBackgroundColor(QColor(255, 255, 255, 255));
                    pItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                }

                return;
            }

            m_isFaceColorOverride[row] = true;
            m_faceColor[row] = inquiryDialog.GetColor();

            if (pItem)
            {
                pItem->setBackgroundColor(m_faceColor[row]);
                pItem->setText(QString::fromStdWString(GBMP_TR(L"")));
            }
        }
        else if (column == 6) // 截面颜色
        {
            if (!CanCategoryHaveGraphicsMaterial(pDoc, m_categories[row]))
                return;

            ColorSettingInquiryDialog inquiryDialog(m_sectionFaceColor[row], m_parent);
            if (inquiryDialog.exec() != QDialog::Accepted)
                return;

            QTableWidgetItem *pItem = m_table->item(row, column);
            DBG_WARN_UNLESS(pItem != nullptr, L"找不到 QTableWidgetItem 对象",L"GDMPLab",L"2024-03-30");

            if (!inquiryDialog.IsToOverrideColor())
            {
                m_isSectionFaceColorOverride[row] = false;
                if (pItem)
                {
                    pItem->setBackgroundColor(QColor(255, 255, 255, 255));
                    pItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                }

                return;
            }

            m_isSectionFaceColorOverride[row] = true;
            m_sectionFaceColor[row] = inquiryDialog.GetColor();

            if (pItem)
            {
                pItem->setBackgroundColor(m_sectionFaceColor[row]);
                pItem->setText(QString::fromStdWString(GBMP_TR(L"")));
            }
        }
        else if (column == 7) // 投影面填充
        {
        }
        else if (column == 8) // 截面填充
        {
        }
        else if (column == 9) // 面透明度
        {
        }
        else if (column == 11) // 投影面填充颜色
        {
            if (!CanCategoryHaveGraphicsMaterial(pDoc, m_categories[row]))
                return;

            ColorSettingInquiryDialog inquiryDialog(QColor(m_faceHatchData[row].m_projectionColor.R, m_faceHatchData[row].m_projectionColor.G, m_faceHatchData[row].m_projectionColor.B), m_parent);
            if (inquiryDialog.exec() != QDialog::Accepted)
                return;

            QTableWidgetItem *pItem = m_table->item(row, column);
            DBG_WARN_UNLESS(pItem != nullptr, L"找不到 QTableWidgetItem 对象",L"GDMPLab",L"2024-03-30");

            if (!inquiryDialog.IsToOverrideColor())
            {
                m_faceHatchData[row].m_isProjectionColorOverride = false;

                if (pItem)
                {
                    pItem->setBackgroundColor(QColor(255, 255, 255, 255));
                    pItem->setText(QString::fromStdWString(GBMP_TR(L"无替换")));
                }
                return;
            }

            m_faceHatchData[row].m_isProjectionColorOverride = true;
            m_faceHatchData[row].m_projectionColor.R = inquiryDialog.GetColor().red();
            m_faceHatchData[row].m_projectionColor.G = inquiryDialog.GetColor().green();
            m_faceHatchData[row].m_projectionColor.B = inquiryDialog.GetColor().blue();
            m_faceHatchData[row].m_projectionColor.A = inquiryDialog.GetColor().alpha();

            if (pItem)
            {
                pItem->setBackgroundColor(QColor(m_faceHatchData[row].m_projectionColor.R, m_faceHatchData[row].m_projectionColor.G, m_faceHatchData[row].m_projectionColor.B, m_faceHatchData[row].m_projectionColor.A));
                pItem->setText(QString::fromStdWString(GBMP_TR(L"")));
            }
        }
    }
}

std::vector<std::wstring> GraphicsStyleTable::CalcItemNames_inMaterialComboBox()
{
    std::vector<std::wstring> itemNames;
    // 文档中所有材质实例(material guid)
    std::vector<IGraphicsMaterial*> graphicsMaterials = IGraphicsMaterial::GetAllGraphicsMaterials(m_wpUIDoc->GetDbDocument());
    for (const IGraphicsMaterial* pGraphicsMaterial : graphicsMaterials)
    {
        // 针对类别的材质和材质实例不是一一绑定的，所以忽略
        if (pGraphicsMaterial->GetTargetCategoryUid().IsValid())
            continue;

        std::wstring materialName;
        bool isOk = pGraphicsMaterial->GetMaterialInstanceName(materialName);
        DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pGraphicsMaterial->GetMaterialInstanceName(materialName) 返回 false",L"GDMPLab",L"2024-03-30");
        itemNames.push_back(materialName);
    }

    itemNames.push_back(MORE_MATERIAL);
    itemNames.push_back(NO_MATERIAL);

    return itemNames;
}

void GraphicsStyleTable::UpdateComboBoxs_4_materialAdded(int categoryIndex, const std::wstring& materialName)
{
    // 计算出材质下拉列表中可见的内容
    std::vector<std::wstring> itemNames = CalcItemNames_inMaterialComboBox();

    FOR_EACH(materialComboBox, m_materialComboBoxList)
    {
        int row = materialComboBox->property("row").toInt();
        int col = materialComboBox->property("col").toInt();
        if (row == categoryIndex) // 就是这个下拉列表中新增了材质
        {
            // 计算下拉列表的当前项
            QStringList itemNames_4_qt;
            int currentComboBoxIndex = 0, iIndex = 0;
            FOR_EACH(itemName, itemNames)
            {
                itemNames_4_qt.push_back(QString::fromStdWString(itemName));
                if (itemName == materialName)
                {
                    currentComboBoxIndex = iIndex;
                }

                iIndex++;
            }

            materialComboBox->blockSignals(true);
            materialComboBox->clear();
            materialComboBox->addItems(itemNames_4_qt);
            materialComboBox->setCurrentIndex(currentComboBoxIndex);
            materialComboBox->blockSignals(false);
        }
        else
        {
            std::wstring oldMaterialName = materialComboBox->currentText().toStdWString();

            // 计算下拉列表的当前项
            QStringList itemNames_4_qt;
            int currentComboBoxIndex = 0, iIndex = 0;
            FOR_EACH(itemName, itemNames)
            {
                itemNames_4_qt.push_back(QString::fromStdWString(itemName));
                if (itemName == oldMaterialName)
                {
                    currentComboBoxIndex = iIndex;
                }

                iIndex++;
            }

            materialComboBox->blockSignals(true);
            materialComboBox->clear();
            materialComboBox->addItems(itemNames_4_qt);
            materialComboBox->setCurrentIndex(currentComboBoxIndex);
            materialComboBox->blockSignals(false);
        }
    }
}

void GraphicsStyleTable::OnClickLineTypeCell(int iButtonIndex)
{
    // 选择线型。
    LineTypeDlg dlg;
    if (dlg.exec() == QDialog::Accepted)
    {
        QString selectLineTypeName = dlg.GetSelectLineTypeName();
        QPushButton* pLineTypeButton = dynamic_cast<QPushButton*>(m_opLineTypeBtnGroup->button(iButtonIndex));
        pLineTypeButton->setText(selectLineTypeName);
    }
}

void GraphicsStyleTable::OnMaterialComboBox_currentIndexChanged(int index)
{
    IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"m_wpUIDoc->GetDbDocument() 返回空指针",L"GDMPLab",L"2024-03-30");

    // 当前类别的材质element
    int row = sender()->property("row").toInt();
    int col = sender()->property("col").toInt();
    const UniIdentity& categoryUid = m_categories[row];

    const IGraphicsMaterialManager* pGraphicsMaterialManager = pDoc->GetGraphicsMaterialManager();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGraphicsMaterialManager, L"pDoc->GetGraphicsMaterialManager() 返回空指针",L"GDMPLab",L"2024-03-30");
    ElementId materialElementId = pGraphicsMaterialManager->GetGraphicsMaterialIdByCategoryUid(categoryUid);
    // 如果绑定了实例，则打开材质库时在项目材质库中默认选中materialElementId对应的材质实例
    ElementId materialElementIdToShow = ElementId::InvalidID;
    IGraphicsMaterial* pGraphicsMaterial = quick_cast<IGraphicsMaterial>(pDoc->GetElement(materialElementId));
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGraphicsMaterial, L"DocUtil::GetElement<IGraphicsMaterial>(pDoc, newMaterialElementId) 返回空指针",L"GDMPLab",L"2024-03-30");
    if (pGraphicsMaterial->IsBoundWithMaterialInstance())
        materialElementIdToShow = materialElementId;

    QComboBox* pComboBox = (QComboBox*)m_table->cellWidget(row, col);
    int iIndex = pComboBox->currentIndex();
    std::wstring itemText = pComboBox->itemText(iIndex).toStdWString();
    if (itemText == MORE_MATERIAL)
    {
        //日志回放时，忽略对材质的设置
        if (gcmp::JournalUtils::IsInReplay())
            return;

        // 选择材质实例
        ElementId newMaterialElementId;
        UiCommonDialog::ButtonType res = UiCommonDialog::ButtonType::Invalid;
        if (DEBUG_MODE(DARKMATERIALSELECTIONDLG))
            res = UiCommonDialog::ShowMaterialSelectionDialog(pDoc, newMaterialElementId, L"", L"", L"", UiCommonDialog::MaterialSelecitonDialogTheme::Dark, materialElementIdToShow, L"木纹");
        else
            res = UiCommonDialog::ShowMaterialSelectionDialog(pDoc, newMaterialElementId, L"", L"", L"", UiCommonDialog::MaterialSelecitonDialogTheme::White, materialElementIdToShow, L"木纹");
        if (res == UiCommonDialog::ButtonType::OK && newMaterialElementId.IsValid())
        {
            IGraphicsMaterial* pGraphicsMaterial = quick_cast<IGraphicsMaterial>(pDoc->GetElement(newMaterialElementId));
            DBG_WARN_AND_RETURN_VOID_UNLESS(pGraphicsMaterial, L"DocUtil::GetElement<IGraphicsMaterial>(pDoc, newMaterialElementId) 返回空指针",L"GDMPLab",L"2024-03-30");

            std::wstring materialName;
            bool isOk = pGraphicsMaterial->GetMaterialInstanceName(materialName);
            DBG_WARN_AND_RETURN_VOID_UNLESS(isOk, L"pGraphicsMaterial->GetMaterialInstanceName(materialName) 返回 false",L"GDMPLab",L"2024-03-30");

            // 这个类别新增了一个材质，所有的下拉列表都需更新
            UpdateComboBoxs_4_materialAdded(row, materialName);

            // 记录修改
            m_materialChangeData[materialElementId] = newMaterialElementId;
        }
    }
    else if (itemText == NO_MATERIAL)
    {
        m_materialChangeData[materialElementId] = ElementId::InvalidID; // 记录下材质的修改
    }
    else
    {
        bool isMaterialFound = false;
        std::vector<IGraphicsMaterial*> graphicsMaterials = IGraphicsMaterial::GetAllGraphicsMaterials(m_wpUIDoc->GetDbDocument());
        for (const IGraphicsMaterial* pGraphicsMaterial : graphicsMaterials)
        {
            // 针对类别的材质和材质实例不是一一绑定的，所以忽略
            if (pGraphicsMaterial->GetTargetCategoryUid().IsValid())
                continue;

            std::wstring newMaterialName;
            bool isOk = pGraphicsMaterial->GetMaterialInstanceName(newMaterialName);
            DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pGraphicsMaterial->GetMaterialInstanceName(materialName) 返回 false",L"GDMPLab",L"2024-03-30");
            if (newMaterialName == itemText)
            {
                m_materialChangeData[materialElementId] = pGraphicsMaterial->GetElementId(); // 记录下材质的修改
                isMaterialFound = true;
                break;
            }
        }

        DBG_WARN_UNLESS(isMaterialFound, L"没有匹配的材质？",L"GDMPLab",L"2024-03-30");
    }
}

bool GraphicsStyleTable::CanCategoryHaveGraphicsMaterial(const IDocument* pDocument, const UniIdentity& categoryUid) const
{
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pDocument, L"pDocument为空",L"GDMPLab",L"2024-03-30");

    // 得到类别
    const ICategoryLibrary* pCategoryLib = ICategoryLibrary::Get(const_cast<IDocument*>(pDocument));
    DBG_WARN_AND_RETURN_FALSE_UNLESS(pCategoryLib, L"pCategoryLib为空",L"GDMPLab",L"2024-03-30");

    const ICategory* pCategory = pCategoryLib->GetCategory(categoryUid);
    if (pCategory == nullptr)
        return false;

    // 从类别中获取是否允许设置材质
    return pCategory->CanHaveGraphicsMaterial();
}

void GraphicsStyleTable::UpdateMaterialChanges_to_doc()
{
    gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"m_wpUIDoc->GetDbDocument() 返回空指针",L"GDMPLab",L"2024-03-30");
    const IGraphicsMaterialManager* pMaterialMgr = pDoc->GetGraphicsMaterialManager();

    std::vector<IModelView*> modelViews = IModelView::GetAllModelViews(pDoc);

    for (std::map<gcmp::ElementId, gcmp::ElementId>::const_iterator iter = m_materialChangeData.begin(); iter != m_materialChangeData.end(); iter++)
    {
        IGraphicsMaterial* pMaterialElement = quick_cast<IGraphicsMaterial>(m_wpUIDoc->GetDbDocument()->GetElement(iter->first));
        DBG_WARN_AND_CONTINUE_UNLESS(pMaterialElement->GetTargetCategoryUid().IsValid(), L"只有针对类别的 IGraphicsMaterial 才能替换材质实例",L"GDMPLab",L"2024-03-30");

        UniIdentity categoryUid = pMaterialElement->GetTargetCategoryUid();
        DBG_WARN_AND_CONTINUE_UNLESS(categoryUid.IsValid(), L"pMaterialElement->GetTargetCategoryUid() 返回的 UniIdentity 无效",L"GDMPLab",L"2024-03-30");

        ElementId docMaterialId = pMaterialMgr->GetGraphicsMaterialIdByCategoryUid(categoryUid);
        DBG_WARN_AND_CONTINUE_UNLESS(docMaterialId.IsValid(), L"类别不能拥有材质",L"GDMPLab",L"2024-03-30");

        ElementId newMaterialId = iter->second;
        if (newMaterialId.IsValid())
        {
            bool isOk = pMaterialElement->UnbindMaterialInstance();
            DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pMaterialElement->UnbindMaterialInstance() 返回 false",L"GDMPLab",L"2024-03-30");
            isOk = pMaterialElement->BindMaterialInstance(newMaterialId);
            DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pMaterialElement->BindMaterialInstance(newMaterialId) 返回 false",L"GDMPLab",L"2024-03-30");

            // 文档级类别材质的改动同步到视图级
            if (!IsViewGraphicsStyle())
            {
                OwnerPtr<IGraphicsMaterialData> opDocMaterialData = pMaterialElement->GetGraphicsMaterialDataFromMaterialInstance();
                DBG_WARN_AND_CONTINUE_UNLESS(opDocMaterialData, L"pMaterialElement->GetGraphicsMaterialDataFromMaterialInstance() 返回空指针",L"GDMPLab",L"2024-03-30");

                FOR_EACH(pCurModelView, modelViews)
                {
                    DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"dynamic_cast<ModelView*>(pModelView) 返回空指针",L"GDMPLab",L"2024-03-30");

                    OwnerPtr<IGraphicsMaterialData> opViewMaterialData = pCurModelView->GetGraphicsMaterialOverrideByCategoryUid(categoryUid);
                    if (!opViewMaterialData) // 视图中没有重载类别的材质，不需要同步
                        continue;

                    double transparency = opViewMaterialData->GetTransparency();
                    opViewMaterialData = opDocMaterialData->Clone();
                    opViewMaterialData->SetTransparency(transparency);

                    pCurModelView->OverrideGraphicsMaterialByCategoryUid(categoryUid, opViewMaterialData.get());
                }
            }
        }
        else
        {
            bool isOk = pMaterialElement->UnbindMaterialInstance();
            DBG_WARN_AND_CONTINUE_UNLESS(isOk, L"pMaterialElement->UnbindMaterialInstance() 返回 false",L"GDMPLab",L"2024-03-30");

            // 文档级类别材质的改动同步到视图级
            if (!IsViewGraphicsStyle())
            {
                FOR_EACH(pCurModelView, modelViews)
                {
                    DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"dynamic_cast<ModelView*>(pModelView) 返回空指针",L"GDMPLab",L"2024-03-30");

                    OwnerPtr<IGraphicsMaterialData> opViewMaterialData = pCurModelView->GetGraphicsMaterialOverrideByCategoryUid(categoryUid);
                    if (!opViewMaterialData) // 视图中没有重载类别的材质，不需要同步
                        continue;

                    pCurModelView->OverrideGraphicsMaterialByCategoryUid(categoryUid, nullptr);
                }
            }
        }
    }
}

void GraphicsStyleTable::OnApplied()
{
    if (m_wpUIDoc.Get() == nullptr)
    {
        return;
    }

    gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-03-30");

    std::set<UniIdentity> allVisibleCategories = GraphicsStyleDlg::GetAllCategoriesWithGStyles(pDoc);
    IGraphicsStyleManager* pGStyleManager = pDoc->GetGraphicsStyleManager();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pGStyleManager, L"pGStyleManager 为空",L"GDMPLab",L"2024-03-30");

    int categoryIndex = -1;
    FOR_EACH(categoryUid, m_categories) // 这一页准备显示的类别
    {
        // 看看文档中是否有这个类别
        bool bExistInDoc = std::find(allVisibleCategories.begin(), allVisibleCategories.end(), categoryUid) != allVisibleCategories.end();
        // 保存这个类别
        if (bExistInDoc)
        {
            ++categoryIndex;

            ElementId syleId = pGStyleManager->GetGraphicsStyleIdByCategoryUid(categoryUid);
            IGraphicsStyle* pDocGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(syleId));
            DBG_WARN_AND_CONTINUE_UNLESS(pDocGStyle, L"pDocGStyle 为空",L"GDMPLab",L"2024-03-30");
            // 如果前景样式不存在，则创建
            ElementId foregroundSyleId = pGStyleManager->GetForegroundGraphicsStyleIdByCategoryUid(categoryUid);
            if (!foregroundSyleId.IsValid())
            {
                OwnerPtr<IGraphicsStyleData> opGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                foregroundSyleId = pGStyleManager->CreateForegroundGraphicsStyleForCategory(categoryUid, *opGStyleDataTmp);
            }

            // 处理重载视图样式
            if (IsViewGraphicsStyle())
            {
                IModelView *pModelView = GetModelView();
                DBG_WARN_AND_CONTINUE_UNLESS(pModelView, L"dynamic_cast<ModelView*>(GetModelView()) 返回空指针",L"GDMPLab",L"2024-03-30");

                // 如果目标类别没有重载样式，则重载(样式数据使用文档级别的样式数据)。如果已有重载，则不作任何事
                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opGStyleOverriddenItems = pModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                pModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opGStyleOverriddenItems.first, *opGStyleOverriddenItems.second);

                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opForegroundGStyleOverriddenItems = pModelView->GetForegroundGraphicsStyleOverrideByCategoryUid(categoryUid);
                pModelView->OverrideForegroundGraphicsStyleByCategoryUid(categoryUid, *opForegroundGStyleOverriddenItems.first, *opForegroundGStyleOverriddenItems.second);

                // todo: 因为现在视图对象样式中已经不能整个重载材质了，因此此处需要删除
                //日志回放时，已在CmdViewMaterialSet中做了处理
                if (!gcmp::JournalUtils::IsInReplay())
                {
                    // todo: 考虑与文档级材质同步问题

                    // 更新材质的修改
                    UpdateMaterialChanges_to_doc();

                    //RecordMaterialCommand(ID_CMD_VIEW_MATERIAL_SET, m_materialIds[categoryIndex], categoryUid);
                }
            }
            else
            {
                //日志回放时，已在CmdProjectMaterialSet中做了处理
                if (!gcmp::JournalUtils::IsInReplay())
                {
                    // todo: 需要考虑和视图级材质的同步问题

                    // 更新材质的修改
                    UpdateMaterialChanges_to_doc();

                    //RecordMaterialCommand(ID_CMD_PROJECT_MATERIAL_SET, m_materialIds[categoryIndex], categoryUid);
                }
            }

            // 修改样式
            if (IsViewGraphicsStyle())
            {
                IModelView *pModelView = GetModelView();
                DBG_WARN_AND_CONTINUE_UNLESS(pModelView, L"dynamic_cast<ModelView*>(GetModelView()) 返回空指针",L"GDMPLab",L"2024-03-30");

                OwnerPtr<IGraphicsStyleData> opDocGStyleData = pDocGStyle->GetGraphicsStyleData();

                //设置可见性
                {
                    QCheckBox *comBox = dynamic_cast<QCheckBox*>(m_table->cellWidget(categoryIndex, 0));
                    if (comBox != nullptr)
                    {
                        bool isVisible = comBox->isChecked() ? true : false;
                        if (IsViewGraphicsStyle())
                        {
                            //如果在view模式下，修改View的对应可见性属性
                            GetModelView()->SetElementsVisibilityByCategoryUid(categoryUid, isVisible);
                        }
                    }
                }

                // 当前视图指定类别的重载情况以及样式数据
                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opViewGStyleOverriddenItemsAndData = pModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                OwnerPtr<IGraphicsStyleDataOverrideItems>& curCatViewGStyleOverriddenItems = opViewGStyleOverriddenItemsAndData.first;
                OwnerPtr<IGraphicsStyleData>& curCatViewGStyleData = opViewGStyleOverriddenItemsAndData.second;

                //投影线属性
                {
                    int cellIndex = buttonOffset * 2 + categoryIndex;
                    auto it = m_cellIndexLineStyleMap.find(cellIndex);
                    if (it != m_cellIndexLineStyleMap.end())
                    {
                        LineStyle lineStyle = it->second;
                        if (lineStyle.hasColorOverride)
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionLineColor(true);
                            curCatViewGStyleData->SetProjectionLineColor(lineStyle.lineColor);
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionLineColor(false);
                            curCatViewGStyleData->SetProjectionLineColor(opDocGStyleData->GetProjectionLineColor());
                        }

                        if (lineStyle.lineWidth == -1)
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionLineWidth(false);
                            curCatViewGStyleData->SetProjectionLineWidth(opDocGStyleData->GetProjectionLineWidth());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionLineWidth(true);
                            curCatViewGStyleData->SetProjectionLineWidth(lineStyle.lineWidth);
                        }

                        if (lineStyle.lineType == GBMP_TR(L"无替换"))
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionLineTypeName(false);
                            curCatViewGStyleData->SetProjectionLineTypeName(opDocGStyleData->GetProjectionLineTypeName());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionLineTypeName(true);
                            curCatViewGStyleData->SetProjectionLineTypeName(lineStyle.lineType);
                        }
                    }
                }

                //截面线属性
                {
                    int cellIndex = buttonOffset * 3 + categoryIndex;
                    auto it = m_cellIndexLineStyleMap.find(cellIndex);
                    if (it != m_cellIndexLineStyleMap.end())
                    {
                        LineStyle lineStyle = it->second;
                        if (lineStyle.hasColorOverride)
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionLineColor(true);
                            curCatViewGStyleData->SetSectionLineColor(lineStyle.lineColor);
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionLineColor(false);
                            curCatViewGStyleData->SetSectionLineColor(opDocGStyleData->GetSectionLineColor());
                        }

                        if (lineStyle.lineWidth == -1)
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionLineWidth(false);
                            curCatViewGStyleData->SetSectionLineWidth(opDocGStyleData->GetSectionLineWidth());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionLineWidth(true);
                            curCatViewGStyleData->SetSectionLineWidth(lineStyle.lineWidth);
                        }

                        if (lineStyle.lineType == GBMP_TR(L"无替换"))
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionLineTypeName(false);
                            curCatViewGStyleData->SetSectionLineTypeName(opDocGStyleData->GetSectionLineTypeName());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionLineTypeName(true);
                            curCatViewGStyleData->SetSectionLineTypeName(lineStyle.lineType);
                        }
                    }
                }

                //隐藏线属性
                {
                    int cellIndex = buttonOffset * 4 + categoryIndex;
                    auto it = m_cellIndexLineStyleMap.find(cellIndex);
                    if (it != m_cellIndexLineStyleMap.end())
                    {
                        LineStyle lineStyle = it->second;
                        if (lineStyle.hasColorOverride)
                        {
                            curCatViewGStyleOverriddenItems->OverrideHiddenLineColor(true);
                            curCatViewGStyleData->SetHiddenLineColor(lineStyle.lineColor);
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideHiddenLineColor(false);
                            curCatViewGStyleData->SetHiddenLineColor(opDocGStyleData->GetHiddenLineColor());
                        }

                        if (lineStyle.lineWidth == -1)
                        {
                            curCatViewGStyleOverriddenItems->OverrideHiddenLineWidth(false);
                            curCatViewGStyleData->SetHiddenLineWidth(opDocGStyleData->GetHiddenLineWidth());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideHiddenLineWidth(true);
                            curCatViewGStyleData->SetHiddenLineWidth(lineStyle.lineWidth);
                        }

                        if (lineStyle.lineType == GBMP_TR(L"无替换"))
                        {
                            curCatViewGStyleOverriddenItems->OverrideHiddenLineTypeName(false);
                            curCatViewGStyleData->SetHiddenLineTypeName(opDocGStyleData->GetHiddenLineTypeName());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideHiddenLineTypeName(true);
                            curCatViewGStyleData->SetHiddenLineTypeName(lineStyle.lineType);
                        }
                    }
                }

                // 投影面颜色
                {
                    if (CanCategoryHaveGraphicsMaterial(m_wpUIDoc->GetDbDocument(), categoryUid))
                    {
                        if (m_isFaceColorOverride[categoryIndex])
                        {
                            curCatViewGStyleOverriddenItems->OverrideColor(true);
                            curCatViewGStyleData->SetColor(Color(
                                m_faceColor[categoryIndex].red()
                                , m_faceColor[categoryIndex].green()
                                , m_faceColor[categoryIndex].blue()
                                , m_faceColor[categoryIndex].alpha()));
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideColor(false);
                            curCatViewGStyleData->SetColor(opDocGStyleData->GetColor());
                        }
                    }
                }
                
                // 截面颜色
                {
                    if (CanCategoryHaveGraphicsMaterial(m_wpUIDoc->GetDbDocument(), categoryUid))
                    {
                        if (m_isSectionFaceColorOverride[categoryIndex])
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionFaceColor(true);
                            curCatViewGStyleData->SetSectionFaceColor(Color(
                                m_sectionFaceColor[categoryIndex].red()
                                , m_sectionFaceColor[categoryIndex].green()
                                , m_sectionFaceColor[categoryIndex].blue()
                                , m_sectionFaceColor[categoryIndex].alpha()));
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionFaceColor(false);
                            curCatViewGStyleData->SetSectionFaceColor(opDocGStyleData->GetSectionFaceColor());
                        }
                    }
                }

                // 面填充
                if (CanCategoryHaveGraphicsMaterial(m_wpUIDoc->GetDbDocument(), categoryUid))
                {
                    IDocument* pDoc = m_wpUIDoc->GetDbDocument();
                    DBG_WARN_AND_CONTINUE_UNLESS(pDoc, L"pDoc为空",L"GDMPLab",L"2024-03-30");
                    std::vector<std::wstring> hatchPatternNames;
                    std::vector<IElement*> HatchPatternElements = pDoc->GetElementsByCategory(BuiltInCategoryUniIdentities::BICU_HATCH_PATTERN);
                    FOR_EACH(pHatchPatternElement, HatchPatternElements)
                    {
                        IGenericElement* pHatchPatternEle = quick_cast<IGenericElement>(pHatchPatternElement);
                        if (!pHatchPatternEle)
                        {
                            continue;
                        }
                        IExternalObject* pExternalObject = pHatchPatternEle->GetExternalObject();
                        DBG_WARN_AND_CONTINUE_UNLESS(pExternalObject, L"pExternalObject为空",L"GDMPLab",L"2024-03-30");
                        IFillPattern* pHatchPatternElement = quick_cast<IFillPattern>(pExternalObject);
                        DBG_WARN_AND_CONTINUE_UNLESS(pHatchPatternElement, L"pHatchPatternElement为空",L"GDMPLab",L"2024-03-30");
                        const IFillPatternData* pHatchPattern = pHatchPatternElement->GetFillPatternData();
                        DBG_WARN_AND_CONTINUE_UNLESS(pHatchPattern, L"pHatchPattern1为空",L"GDMPLab",L"2024-03-30");
                        std::wstring name = pHatchPattern->GetName();
                        hatchPatternNames.push_back(name);
                    }
                    QComboBox *comBox = nullptr;

                    // 投影面
                    comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 7));
                    if (!comBox)
                    {
                        DBG_WARN(L"下拉框不存在？",L"GDMPLab",L"2024-03-30");
                    }
                    else if (!comBox->isEnabled())
                    {
                    }
                    else
                    {
                        if (comBox->currentIndex() == 0)
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionFaceHatchPattern(false);
                            curCatViewGStyleData->SetProjectionFaceHatchPattern(opDocGStyleData->GetProjectionFaceHatchPattern());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionFaceHatchPattern(true);
                            curCatViewGStyleData->SetProjectionFaceHatchPattern(hatchPatternNames[comBox->currentIndex() - 1]);
                        }
                    }

                    // 截面
                    comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 8));
                    if (!comBox)
                    {
                        DBG_WARN(L"下拉框不存在？",L"GDMPLab",L"2024-03-30");
                    }
                    else if (!comBox->isEnabled())
                    {
                    }
                    else
                    {
                        if (comBox->currentIndex() == 0)
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionFaceHatchPattern(false);
                            curCatViewGStyleData->SetSectionFaceHatchPattern(opDocGStyleData->GetProjectionFaceHatchPattern());
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideSectionFaceHatchPattern(true);
                            curCatViewGStyleData->SetSectionFaceHatchPattern(hatchPatternNames[comBox->currentIndex() - 1]);
                        }
                    }
                }

                // 面透明度
                {
                    const IGraphicsMaterialManager* pMaterialMgr = pDoc->GetGraphicsMaterialManager();
                    ElementId docMaterialId = pMaterialMgr->GetGraphicsMaterialIdByCategoryUid(categoryUid);

                    if (docMaterialId.IsValid())
                    {
                        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(categoryIndex, 9));
                        if (!lineEdit)
                        {
                            DBG_WARN(L"下拉框不存在？",L"GDMPLab",L"2024-03-30");
                        }
                        else
                        {
                            while (true)
                            {
                                bool bval;
                                int ival = lineEdit->text().toInt(&bval);
                                DBG_WARN_AND_BREAK_UNLESS(bval && ival >= 0 && ival <= 255, L"从控件获取的透明度值无法转换成[0,255]之间的整数",L"GDMPLab",L"2024-03-30");

                                double transparencyVal = ival / 255.0;
                                MathUtils::Clamp(0.0, 1.0, transparencyVal);

                                if (MathUtils::IsEqual(transparencyVal, curCatViewGStyleData->GetTransparency()))
                                    break;

                                curCatViewGStyleOverriddenItems->OverrideTransparency(true);
                                curCatViewGStyleData->SetTransparency(transparencyVal);

                                IGraphicsMaterial* pDocMaterial = quick_cast<IGraphicsMaterial>(pDoc->GetElement(docMaterialId));
                                DBG_WARN_AND_BREAK_UNLESS(pDocMaterial, L"quick_cast<IGraphicsMaterial>(pDoc->GetElement(docMaterialId)) 返回空指针",L"GDMPLab",L"2024-03-30");

                                if (!pDocMaterial->IsBoundWithMaterialInstance())
                                    break;

                                OwnerPtr<IGraphicsMaterialData> opDocMaterialData = pDocMaterial->GetGraphicsMaterialDataFromMaterialInstance();
                                DBG_WARN_AND_BREAK_UNLESS(opDocMaterialData, L"pDocMaterial->GetGraphicsMaterialDataFromMaterialInstance() 返回空指针",L"GDMPLab",L"2024-03-30");
                                opDocMaterialData->SetTransparency(transparencyVal);
                                pModelView->OverrideGraphicsMaterialByCategoryUid(categoryUid, opDocMaterialData.get());

                                break;
                            }
                        }
                    }
                }

                // 详细程度
                {
                    const IGraphicsMaterialManager* pMaterialMgr = pDoc->GetGraphicsMaterialManager();
                    ElementId docMaterialId = pMaterialMgr->GetGraphicsMaterialIdByCategoryUid(categoryUid);

                    QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 10));

                    int idx = comBox->currentIndex() - 1;

                    if (idx >= -1 && idx <= 2)
                    {
                        pModelView->OverrideDetailLevelByCategoryUid(categoryUid, ModelViewDetailLevel(idx));
                    }
                    else
                    {
                        DBG_WARN(L"获取组合框索引有问题？",L"GDMPLab",L"2024-03-30");
                    }

                }

                // 投影面填充颜色
                {
                    if (CanCategoryHaveGraphicsMaterial(m_wpUIDoc->GetDbDocument(), categoryUid))
                    {
                        if (m_faceHatchData[categoryIndex].m_isProjectionColorOverride)
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionFaceHatchColor(true);

                            curCatViewGStyleData->SetProjectionFaceHatchColor(Color(
                                m_faceHatchData[categoryIndex].m_projectionColor.R
                                , m_faceHatchData[categoryIndex].m_projectionColor.G
                                , m_faceHatchData[categoryIndex].m_projectionColor.B
                                , m_faceHatchData[categoryIndex].m_projectionColor.A));
                        }
                        else
                        {
                            curCatViewGStyleOverriddenItems->OverrideProjectionFaceHatchColor(false);
                            curCatViewGStyleData->SetProjectionFaceHatchColor(opDocGStyleData->GetProjectionFaceHatchColor());
                        }
                    }
                }

                //统一设置视图样式重载数据
                pModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *curCatViewGStyleOverriddenItems, *curCatViewGStyleData);
                // 上投影线属性
                {
                    // 当前视图指定类别的前景对象的重载情况以及前景样式数据
                    std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opViewForegroundGStyleOverriddenItemsAndData = pModelView->GetForegroundGraphicsStyleOverrideByCategoryUid(categoryUid);
                    OwnerPtr<IGraphicsStyleDataOverrideItems>& curViewForegroundGStyleOverriddenItems = opViewForegroundGStyleOverriddenItemsAndData.first;
                    OwnerPtr<IGraphicsStyleData>& curForegroundViewGStyleData = opViewForegroundGStyleOverriddenItemsAndData.second;
                    {
                        OwnerPtr<IGraphicsStyleDataValiditySettings> pGStyleDataSettings = IGraphicsStyleDataValiditySettings::Create();
                        pGStyleDataSettings->ValidateNone();
                        pGStyleDataSettings->SetProjectionLineColorValidity(true);
                        pGStyleDataSettings->SetProjectionLineWidthValidity(true);
                        pGStyleDataSettings->SetProjectionLineTypeNameValidity(true);
                        curForegroundViewGStyleData->SetDataValiditySettings(*pGStyleDataSettings);
                    }

                    //文档级别的类别前景样式数据
                    DBG_WARN_AND_RETURN_VOID_UNLESS(foregroundSyleId.IsValid(), L"前景样式创建失败",L"GDMPLab",L"2024-03-30");
                    IGraphicsStyle* pDocForegroundGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(foregroundSyleId));
                    DBG_WARN_AND_RETURN_VOID_UNLESS(pDocForegroundGStyle, L"pDocForegroundGStyle为空",L"GDMPLab",L"2024-03-30");
                    OwnerPtr<IGraphicsStyleData> opDocForegroundGStyleData = pDocForegroundGStyle->GetGraphicsStyleData();
                    DBG_WARN_AND_CONTINUE_UNLESS(opDocForegroundGStyleData, L"opDocForegroundGStyleData为空",L"GDMPLab",L"2024-03-30");
                    
                    int cellIndex = buttonOffset * 12 + categoryIndex;
                    auto it = m_cellIndexLineStyleMap.find(cellIndex);
                    if (it != m_cellIndexLineStyleMap.end())
                    {
                        LineStyle lineStyle = it->second;
                        if (lineStyle.hasColorOverride)
                        {
                            curViewForegroundGStyleOverriddenItems->OverrideProjectionLineColor(true);
                            curForegroundViewGStyleData->SetProjectionLineColor(lineStyle.lineColor);
                        }
                        else
                        {
                            curViewForegroundGStyleOverriddenItems->OverrideProjectionLineColor(false);
                            curForegroundViewGStyleData->SetProjectionLineColor(opDocForegroundGStyleData->GetProjectionLineColor());
                        }

                        if (lineStyle.lineWidth == -1)
                        {
                            curViewForegroundGStyleOverriddenItems->OverrideProjectionLineWidth(false);
                            curForegroundViewGStyleData->SetProjectionLineWidth(opDocForegroundGStyleData->GetProjectionLineWidth());
                        }
                        else
                        {
                            curViewForegroundGStyleOverriddenItems->OverrideProjectionLineWidth(true);
                            curForegroundViewGStyleData->SetProjectionLineWidth(lineStyle.lineWidth);
                        }

                        if (lineStyle.lineType == GBMP_TR(L"无替换"))
                        {
                            curViewForegroundGStyleOverriddenItems->OverrideProjectionLineTypeName(false);
                            curForegroundViewGStyleData->SetProjectionLineTypeName(opDocForegroundGStyleData->GetProjectionLineTypeName());
                        }
                        else
                        {
                            curViewForegroundGStyleOverriddenItems->OverrideProjectionLineTypeName(true);
                            curForegroundViewGStyleData->SetProjectionLineTypeName(lineStyle.lineType);
                        }
                    }
                    //统一设置视图前景样式重载数据
                    pModelView->OverrideForegroundGraphicsStyleByCategoryUid(categoryUid, *curViewForegroundGStyleOverriddenItems, *curForegroundViewGStyleData);
                }
            }
            else
            {
                std::vector<IModelView*> modelViews = IModelView::GetAllModelViews(pDoc);

                //设置投影线宽
                {
                    double widthofLine = 0;
                    bool hasValidInput = false;

                    if (IsViewGraphicsStyle())
                    {
                        // 视图样式使用下拉菜单
                        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 1));
                        if (comBox)
                        {
                            int widthIndex = comBox->currentIndex();
                            if (widthIndex > 0) // 跳过"不重载"选项
                            {
                                QString lineWidth = comBox->currentText();
                                widthofLine = lineWidth.toDouble();
                                hasValidInput = true;
                            }
                        }
                    }
                    else
                    {
                        // 文档样式使用直接输入
                        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(categoryIndex, 1));
                        if (lineEdit)
                        {
                            widthofLine = lineEdit->text().toDouble();
                            hasValidInput = true;
                        }
                    }

                    if (hasValidInput)
                    {
                        //文档级别的类别样式数据
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->GetProjectionLineWidth() != widthofLine)
                        {
                            opDocGStyleDataTmp->SetProjectionLineWidth(widthofLine);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                            FOR_EACH(pCurModelView, modelViews)
                            {
                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;

                                if (opCurViewGStyleTmp.first->IsProjectionLineWidthOverridden() == false)
                                {
                                    opCurViewGStyleTmp.second->SetProjectionLineWidth(widthofLine);
                                    pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                }

                //设置投影颜色
                {
                    if (m_colorMap.find(CellType(categoryIndex, 2)) != m_colorMap.end())
                    {
                        int r, g, b, a;
                        QColor qColor = m_colorMap[CellType(categoryIndex, 2)];
                        qColor.getRgb(&r, &g, &b, &a);
                        Color color(r, g, b, a);

                        //文档级别的类别样式数据
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->GetProjectionLineColor() != color)
                        {
                            opDocGStyleDataTmp->SetProjectionLineColor(color);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                            FOR_EACH(pCurModelView, modelViews)
                            {
                                DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;
                                if (!opCurViewGStyleTmp.first->IsProjectionLineColorOverridden())
                                {
                                    opCurViewGStyleTmp.second->SetProjectionLineColor(color);
                                    pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }

                }
                //设置投影线型
                {
                    QAbstractButton* pButton = m_opLineTypeBtnGroup->button(categoryIndex);
                    std::wstring lineTypeName = pButton->text().toStdWString();

                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetProjectionLineTypeName() != lineTypeName)
                    {
                        opDocGStyleDataTmp->SetProjectionLineTypeName(lineTypeName);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;
                            if (!opCurViewGStyleTmp.first->IsProjectionLineTypeNameOverridden())
                            {
                                opCurViewGStyleTmp.second->SetProjectionLineTypeName(lineTypeName);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }

                //设置截面线宽
                {
                    double widthofLine = 0;
                    bool hasValidInput = false;

                    if (IsViewGraphicsStyle())
                    {
                        // 视图样式使用下拉菜单
                        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 4));
                        if (comBox)
                        {
                            int widthIndex = comBox->currentIndex();
                            if (widthIndex > 0) // 跳过"不重载"选项
                            {
                                QString lineWidth = comBox->currentText();
                                widthofLine = lineWidth.toDouble();
                                hasValidInput = true;
                            }
                        }
                    }
                    else
                    {
                        // 文档样式使用直接输入
                        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(categoryIndex, 4));
                        if (lineEdit)
                        {
                            widthofLine = lineEdit->text().toDouble();
                            hasValidInput = true;
                        }
                    }

                    if (hasValidInput)
                    {
                        //文档级别的类别样式数据
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->GetSectionLineWidth() != widthofLine)
                        {
                            opDocGStyleDataTmp->SetSectionLineWidth(widthofLine);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                            FOR_EACH(pCurModelView, modelViews)
                            {
                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;
                                if (!opCurViewGStyleTmp.first->IsSectionLineWidthOverridden())
                                {
                                    opCurViewGStyleTmp.second->SetSectionLineWidth(widthofLine);
                                    pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                }

                //设置截面颜色
                {
                    if (m_colorMap.find(CellType(categoryIndex + buttonOffset, 2)) != m_colorMap.end())
                    {
                        int r, g, b, a;
                        QColor qColor = m_colorMap[CellType(categoryIndex + buttonOffset, 2)];
                        qColor.getRgb(&r, &g, &b, &a);
                        Color color(r, g, b, a);
                        //文档级别的类别样式数据
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->GetSectionLineColor() != color)
                        {
                            opDocGStyleDataTmp->SetSectionLineColor(color);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                            FOR_EACH(pCurModelView, modelViews)
                            {
                                DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;

                                if (!opCurViewGStyleTmp.first->IsSectionLineColorOverridden())
                                {
                                    opCurViewGStyleTmp.second->SetSectionLineColor(color);
                                    pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                }

                //设置截面线型
                {
                    QAbstractButton* pButton = m_opLineTypeBtnGroup->button(categoryIndex + buttonOffset);
                    std::wstring lineTypeName = pButton->text().toStdWString();
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetSectionLineTypeName() != lineTypeName)
                    {
                        opDocGStyleDataTmp->SetSectionLineTypeName(lineTypeName);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsSectionLineTypeNameOverridden())
                            {
                                opCurViewGStyleTmp.second->SetSectionLineTypeName(lineTypeName);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }

                //设置隐藏线宽
                {
                    double widthofLine = 0;
                    bool hasValidInput = false;

                    if (IsViewGraphicsStyle())
                    {
                        // 视图样式使用下拉菜单
                        QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 7));
                        if (comBox)
                        {
                            int widthIndex = comBox->currentIndex();
                            if (widthIndex > 0) // 跳过"不重载"选项
                            {
                                QString lineWidth = comBox->currentText();
                                widthofLine = lineWidth.toDouble();
                                hasValidInput = true;
                            }
                        }
                    }
                    else
                    {
                        // 文档样式使用直接输入
                        QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(categoryIndex, 7));
                        if (lineEdit)
                        {
                            widthofLine = lineEdit->text().toDouble();
                            hasValidInput = true;
                        }
                    }

                    if (hasValidInput)
                    {
                        //文档级别的类别样式数据
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->GetHiddenLineWidth() != widthofLine)
                        {
                            opDocGStyleDataTmp->SetHiddenLineWidth(widthofLine);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                            FOR_EACH(pCurModelView, modelViews)
                            {
                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;
                                if (!opCurViewGStyleTmp.first->IsHiddenLineWidthOverridden())
                                {
                                    opCurViewGStyleTmp.second->SetHiddenLineWidth(widthofLine);
                                    pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                }

                //设置隐藏线颜色
                {
                    if (m_colorMap.find(CellType(categoryIndex + buttonOffset * 2, 2)) != m_colorMap.end())
                    {
                        int r, g, b, a;
                        QColor qColor = m_colorMap[CellType(categoryIndex + buttonOffset * 2, 2)];
                        qColor.getRgb(&r, &g, &b, &a);
                        Color color(r, g, b, a);
                        //文档级别的类别样式数据
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->GetHiddenLineColor() != color)
                        {
                            opDocGStyleDataTmp->SetHiddenLineColor(color);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                            FOR_EACH(pCurModelView, modelViews)
                            {
                                DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;

                                if (!opCurViewGStyleTmp.first->IsHiddenLineColorOverridden())
                                {
                                    opCurViewGStyleTmp.second->SetHiddenLineColor(color);
                                    pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                }

                //设置隐藏线型
                {
                    QAbstractButton* pButton = m_opLineTypeBtnGroup->button(categoryIndex + buttonOffset * 2);
                    std::wstring lineTypeName = pButton->text().toStdWString();
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetHiddenLineTypeName() != lineTypeName)
                    {
                        opDocGStyleDataTmp->SetHiddenLineTypeName(lineTypeName);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsHiddenLineTypeNameOverridden())
                            {
                                opCurViewGStyleTmp.second->SetHiddenLineTypeName(lineTypeName);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }

                // 透明度
                {
                    bool bval = false;
                    QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(categoryIndex, 12));
                    int alphaInt = lineEdit->text().toInt(&bval);
                    DBG_WARN_AND_BREAK_UNLESS(bval && alphaInt >= 0 && alphaInt <= 255, L"从控件获取的透明度值无法转换成[0,255]之间的整数",L"GDMPLab",L"2024-03-30");
                    double doubleAlpha = alphaInt / 255.0;
                    MathUtils::Clamp(0.0, 1.0, doubleAlpha);

                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetTransparency() != doubleAlpha)
                    {
                        opDocGStyleDataTmp->SetTransparency(doubleAlpha);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsColorOverridden())
                            {
                                opCurViewGStyleTmp.second->SetTransparency(doubleAlpha);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }

                // 投影线alpha是否有效
                {
                    QCheckBox *checkBox = dynamic_cast<QCheckBox*>(m_table->cellWidget(categoryIndex, 13));
                    if (checkBox)
                    {
                        bool enable = checkBox->isChecked();
                        OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                        if (opDocGStyleDataTmp->IsProjectionLineColorAlphaEnabled() != enable)
                        {
                            opDocGStyleDataTmp->EnableProjectionLineColorAlpha(enable);
                            pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);
                        }
                    }
                }

                // 面颜色
                {
                    Color faceColor
                    (m_faceColor[categoryIndex].red()
                        , m_faceColor[categoryIndex].green()
                        , m_faceColor[categoryIndex].blue()
                        , m_faceColor[categoryIndex].alpha());

                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetColor() != faceColor)
                    {
                        opDocGStyleDataTmp->SetColor(faceColor);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsColorOverridden())
                            {
                                opCurViewGStyleTmp.second->SetColor(faceColor);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }

                // 截面颜色
                {
                    Color sectionFaceColor
                    (m_sectionFaceColor[categoryIndex].red()
                        , m_sectionFaceColor[categoryIndex].green()
                        , m_sectionFaceColor[categoryIndex].blue()
                        , m_sectionFaceColor[categoryIndex].alpha());
                    
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetSectionFaceColor() != sectionFaceColor)
                    {
                        opDocGStyleDataTmp->SetSectionFaceColor(sectionFaceColor);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsSectionFaceColorOverridden())
                            {
                                opCurViewGStyleTmp.second->SetSectionFaceColor(sectionFaceColor);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }


                // 投影面填充
                // 颜色
                {
                    Color projectionColor = m_faceHatchData[categoryIndex].m_projectionColor;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetProjectionFaceHatchColor() != projectionColor)
                    {
                        opDocGStyleDataTmp->SetProjectionFaceHatchColor(projectionColor);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            opCurViewGStyleTmp.second->SetProjectionFaceHatchColor(projectionColor);
                            pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                        }
                    }
                }
                // 图案

                {
                    std::wstring projectionHatchPattern = m_faceHatchData[categoryIndex].m_projectionPattern;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetProjectionFaceHatchPattern() != projectionHatchPattern)
                    {
                        opDocGStyleDataTmp->SetProjectionFaceHatchPattern(projectionHatchPattern);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsProjectionFaceHatchPatternOverridden())
                            {
                                opCurViewGStyleTmp.second->SetProjectionFaceHatchPattern(projectionHatchPattern);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }
                //角度
                {
                    double projectionHatchAngle = m_faceHatchData[categoryIndex].m_projectionAngle;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetProjectionFaceHatchRotation() != projectionHatchAngle)
                    {
                        opDocGStyleDataTmp->SetProjectionFaceHatchRotation(projectionHatchAngle);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);
                        //同时也设置到HatchPattern里
                        IFillPattern* pHatchPatternElement = IFillPattern::GetFillPatternByName(m_wpUIDoc->GetDbDocument(), m_faceHatchData[categoryIndex].m_projectionPattern);
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPatternElement, L"pHatchPatternElement invalid",L"GDMPLab",L"2024-03-30");
                        const IFillPatternData* pHatchPattern = pHatchPatternElement->GetFillPatternData();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPattern, L"pHatchPattern invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IDbObject> opHatchPatternObject = pHatchPattern->GetCloneBehavior()->CloneObject();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(opHatchPatternObject, L"opHatchPatternObject invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IFillPatternData> opHatchPattern = TransferOwnershipCast<IFillPatternData>(opHatchPatternObject);
                        opHatchPattern->SetRotateAngle(m_faceHatchData[categoryIndex].m_projectionAngle);
                        pHatchPatternElement->SetFillPatternData(TransferOwnership(opHatchPattern));
                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsProjectionFaceHatchPatternOverridden())
                            {
                                opCurViewGStyleTmp.second->SetProjectionFaceHatchRotation(projectionHatchAngle);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }
                //缩放倍数
                {
                    double projectionHatchScale = m_faceHatchData[categoryIndex].m_projectionScale;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetProjectionFaceHatchScale() != projectionHatchScale)
                    {
                   //     opDocGStyleDataTmp->SetProjectionFaceHatchScale(projectionHatchScale);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);
                        //同时也设置到HatchPattern里
                        IFillPattern* pHatchPatternElement = IFillPattern::GetFillPatternByName(m_wpUIDoc->GetDbDocument(), m_faceHatchData[categoryIndex].m_projectionPattern);
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPatternElement, L"pHatchPatternElement invalid",L"GDMPLab",L"2024-03-30");
                        const IFillPatternData* pHatchPattern = pHatchPatternElement->GetFillPatternData();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPattern, L"pHatchPattern invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IDbObject> opHatchPatternObject = pHatchPattern->GetCloneBehavior()->CloneObject();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(opHatchPatternObject, L"opHatchPatternObject invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IFillPatternData> opHatchPattern = TransferOwnershipCast<IFillPatternData>(opHatchPatternObject);
                        opHatchPattern->SetScale(m_faceHatchData[categoryIndex].m_projectionScale);
                        pHatchPatternElement->SetFillPatternData(TransferOwnership(opHatchPattern));
                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsProjectionFaceHatchPatternOverridden())
                            {
                             //   opCurViewGStyleTmp.second->SetProjectionFaceHatchScale(projectionHatchScale);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }
                // 截面填充
                // 颜色
                {
                    Color sectionColor = m_faceHatchData[categoryIndex].m_sectionColor;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetSectionFaceHatchColor() != sectionColor)
                    {
                        opDocGStyleDataTmp->SetSectionFaceHatchColor(sectionColor);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);

                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            opCurViewGStyleTmp.second->SetSectionFaceHatchColor(sectionColor);
                            pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                        }
                    }
                }
                // 图案
                {
                    std::wstring sectionHatchPattern = m_faceHatchData[categoryIndex].m_sectionPattern;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetSectionFaceHatchPattern() != sectionHatchPattern)
                    {
                        opDocGStyleDataTmp->SetSectionFaceHatchPattern(sectionHatchPattern);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);
                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsSectionFaceHatchPatternOverridden())
                            {
                                opCurViewGStyleTmp.second->SetSectionFaceHatchPattern(sectionHatchPattern);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }
                //角度
                {
                    double sectionHatchAngle = m_faceHatchData[categoryIndex].m_sectionAngle;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetSectionFaceHatchRotation() != sectionHatchAngle)
                    {
                        opDocGStyleDataTmp->SetSectionFaceHatchRotation(sectionHatchAngle);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);
                        //同时也设置到HatchPattern里
                        IFillPattern* pHatchPatternElement = IFillPattern::GetFillPatternByName(m_wpUIDoc->GetDbDocument(), m_faceHatchData[categoryIndex].m_sectionPattern);
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPatternElement, L"pHatchPatternElement invalid",L"GDMPLab",L"2024-03-30");
                        const IFillPatternData* pHatchPattern = pHatchPatternElement->GetFillPatternData();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPattern, L"pHatchPattern invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IDbObject> opHatchPatternObject = pHatchPattern->GetCloneBehavior()->CloneObject();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(opHatchPatternObject, L"opHatchPatternObject invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IFillPatternData> opHatchPattern = TransferOwnershipCast<IFillPatternData>(opHatchPatternObject);
                        opHatchPattern->SetRotateAngle(m_faceHatchData[categoryIndex].m_sectionAngle);
                        pHatchPatternElement->SetFillPatternData(TransferOwnership(opHatchPattern));
                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsSectionFaceHatchPatternOverridden())
                            {
                                opCurViewGStyleTmp.second->SetSectionFaceHatchRotation(sectionHatchAngle);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }
                //缩放倍数
                {
                    double sectionHatchScale = m_faceHatchData[categoryIndex].m_sectionScale;
                    //文档级别的类别样式数据
                    OwnerPtr<IGraphicsStyleData> opDocGStyleDataTmp = pDocGStyle->GetGraphicsStyleData();
                    if (opDocGStyleDataTmp->GetSectionFaceHatchScale() != sectionHatchScale)
                    {
              //          opDocGStyleDataTmp->SetSectionFaceHatchScale(sectionHatchScale);
                        pDocGStyle->SetGraphicsStyleData(*opDocGStyleDataTmp);
                        //同时也设置到HatchPattern里
                        IFillPattern* pHatchPatternElement = IFillPattern::GetFillPatternByName(m_wpUIDoc->GetDbDocument(), m_faceHatchData[categoryIndex].m_sectionPattern);
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPatternElement, L"pHatchPatternElement invalid",L"GDMPLab",L"2024-03-30");
                        const IFillPatternData* pHatchPattern = pHatchPatternElement->GetFillPatternData();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(pHatchPattern, L"pHatchPattern invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IDbObject> opHatchPatternObject = pHatchPattern->GetCloneBehavior()->CloneObject();
                        DBG_WARN_AND_RETURN_VOID_UNLESS(opHatchPatternObject, L"opHatchPatternObject invalid",L"GDMPLab",L"2024-03-30");
                        OwnerPtr<IFillPatternData> opHatchPattern = TransferOwnershipCast<IFillPatternData>(opHatchPatternObject);
                        opHatchPattern->SetScale(m_faceHatchData[categoryIndex].m_sectionScale);
                        pHatchPatternElement->SetFillPatternData(TransferOwnership(opHatchPattern));
                        FOR_EACH(pCurModelView, modelViews)
                        {
                            DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                            std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetGraphicsStyleOverrideByCategoryUid(categoryUid);
                            if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                continue;

                            if (!opCurViewGStyleTmp.first->IsSectionFaceHatchPatternOverridden())
                            {
                            //    opCurViewGStyleTmp.second->SetSectionFaceHatchScale(sectionHatchScale);
                                pCurModelView->OverrideGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                            }
                        }
                    }
                }
                // 上投影线样式
                {
                    DBG_WARN_AND_RETURN_VOID_UNLESS(foregroundSyleId.IsValid(), L"前景样式创建失败",L"GDMPLab",L"2024-03-30");
                    IGraphicsStyle* pForegroundGStyle = quick_cast<IGraphicsStyle>(pDoc->GetElement(foregroundSyleId));
                    DBG_WARN_AND_RETURN_VOID_UNLESS(pForegroundGStyle, L"pForegroundGStyle 为空",L"GDMPLab",L"2024-03-30");
                    //文档级别的类别前景样式数据
                    OwnerPtr<IGraphicsStyleData> opGStyleDataTmp = pForegroundGStyle->GetGraphicsStyleData();
                    DBG_WARN_AND_CONTINUE_UNLESS(opGStyleDataTmp, L"opGStyleDataTmp 为空",L"GDMPLab",L"2024-03-30");
                    {
                        OwnerPtr<IGraphicsStyleDataValiditySettings> pGStyleDataSettings = IGraphicsStyleDataValiditySettings::Create();
                        pGStyleDataSettings->ValidateNone();
                        pGStyleDataSettings->SetProjectionLineColorValidity(true);
                        pGStyleDataSettings->SetProjectionLineWidthValidity(true);
                        pGStyleDataSettings->SetProjectionLineTypeNameValidity(true);
                        opGStyleDataTmp->SetDataValiditySettings(*pGStyleDataSettings);
                    }
                    //设置上投影线宽
                    {
                        double widthofLine = 0;
                        bool hasValidInput = false;

                        if (IsViewGraphicsStyle())
                        {
                            // 视图样式使用下拉菜单
                            QComboBox *comBox = dynamic_cast<QComboBox*>(m_table->cellWidget(categoryIndex, 14));
                            if (comBox)
                            {
                                int widthIndex = comBox->currentIndex();
                                if (widthIndex > 0) // 跳过"不重载"选项
                                {
                                    QString lineWidth = comBox->currentText();
                                    widthofLine = lineWidth.toDouble();
                                    hasValidInput = true;
                                }
                            }
                        }
                        else
                        {
                            // 文档样式使用直接输入
                            QLineEdit *lineEdit = dynamic_cast<QLineEdit*>(m_table->cellWidget(categoryIndex, 14));
                            if (lineEdit)
                            {
                                widthofLine = lineEdit->text().toDouble();
                                hasValidInput = true;
                            }
                        }

                        if (hasValidInput && opGStyleDataTmp->GetProjectionLineWidth() != widthofLine)
                        {
                            opGStyleDataTmp->SetProjectionLineWidth(widthofLine);
                            FOR_EACH(pCurModelView, modelViews)
                            {
                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetForegroundGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;

                                if (opCurViewGStyleTmp.first->IsProjectionLineWidthOverridden() == false)
                                {
                                    opCurViewGStyleTmp.second->SetProjectionLineWidth(widthofLine);
                                    pCurModelView->OverrideForegroundGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                    //设置上投影颜色
                    {
                        if (m_colorMap.find(CellType(categoryIndex + buttonOffset * 3, 2)) != m_colorMap.end())
                        {
                            int r, g, b, a;
                            QColor qColor = m_colorMap[CellType(categoryIndex + buttonOffset * 3, 2)];
                            qColor.getRgb(&r, &g, &b, &a);
                            Color color(r, g, b, a);
                            if (opGStyleDataTmp->GetProjectionLineColor() != color)
                            {
                                opGStyleDataTmp->SetProjectionLineColor(color);
                                FOR_EACH(pCurModelView, modelViews)
                                {
                                    DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");

                                    std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetForegroundGraphicsStyleOverrideByCategoryUid(categoryUid);
                                    if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                        continue;
                                    if (!opCurViewGStyleTmp.first->IsProjectionLineColorOverridden())
                                    {
                                        opCurViewGStyleTmp.second->SetProjectionLineColor(color);
                                        pCurModelView->OverrideForegroundGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                    }
                                }
                            }
                        }

                    }
                    //设置上投影线型
                    {
                        QAbstractButton* pButton = m_opLineTypeBtnGroup->button(categoryIndex + buttonOffset * 3);
                        std::wstring lineTypeName = pButton->text().toStdWString();
                        if (opGStyleDataTmp->GetProjectionLineTypeName() != lineTypeName)
                        {
                            opGStyleDataTmp->SetProjectionLineTypeName(lineTypeName);
                            FOR_EACH(pCurModelView, modelViews)
                            {
                                DBG_WARN_AND_CONTINUE_UNLESS(pCurModelView, L"pCurModelView空指针",L"GDMPLab",L"2024-03-30");
                                std::pair<OwnerPtr<IGraphicsStyleDataOverrideItems>, OwnerPtr<IGraphicsStyleData>> opCurViewGStyleTmp = pCurModelView->GetForegroundGraphicsStyleOverrideByCategoryUid(categoryUid);
                                if (opCurViewGStyleTmp.first->IsNoneOverridden() == true)
                                    continue;
                                if (!opCurViewGStyleTmp.first->IsProjectionLineTypeNameOverridden())
                                {
                                    opCurViewGStyleTmp.second->SetProjectionLineTypeName(lineTypeName);
                                    pCurModelView->OverrideForegroundGraphicsStyleByCategoryUid(categoryUid, *opCurViewGStyleTmp.first, *opCurViewGStyleTmp.second);
                                }
                            }
                        }
                    }
                    pForegroundGStyle->SetGraphicsStyleData(*opGStyleDataTmp);
                }
            }
        }
    }
}

gcmp::IModelView* GraphicsStyleTable::GetModelView() const
{
    return m_wpCurrentView ? m_wpCurrentView->GetModelView() : nullptr;
}

void GraphicsStyleTable::AddVisibilityCtrlToTable(QTableWidget* pTable, const gcmp::IModelView* pModelView, const gcmp::UniIdentity& categoryUid, int row, int col)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(pModelView && IsViewGraphicsStyle(), L"pModelView is null",L"GDMPLab",L"2024-03-30");

    //给可见性添加CheckBox
    //在视图的模式下，看是否有被覆盖过的可见性属性    
    bool isVisible = GetModelView()->GetElementsVisibilityByCategoryUid(categoryUid);

    gcmp::OwnerPtr<QCheckBox> pCheckBox(NEW_AS_OWNER_PTR(QCheckBox));
    if (isVisible)
    {
        pCheckBox->setChecked(true);
    }
    else
    {
        pCheckBox->setChecked(false);
    }
    pTable->setCellWidget(row, col, TRANSFER_OWNERSHIP_TO_THIRD_PARTY(pCheckBox));
}

void GraphicsStyleTable::RecordMaterialCommand(const std::wstring& cmdName, gcmp::ElementId materialId, const gcmp::UniIdentity& categoryUniIdentity)
{
    DBG_WARN_AND_RETURN_VOID_UNLESS(m_wpUIDoc, L"m_wpUIDoc == nullptr",L"GDMPLab",L"2024-03-30");
    gcmp::IDocument* pDoc = m_wpUIDoc->GetDbDocument();
    DBG_WARN_AND_RETURN_VOID_UNLESS(pDoc, L"doc 为空",L"GDMPLab",L"2024-03-30");

    // 输出材质参数到日志
    IGraphicsMaterial* pMaterialElement = quick_cast<IGraphicsMaterial>(pDoc->GetElement(materialId));
    gcmp::CommandParameters paramsMap;
    if (pMaterialElement != nullptr && pMaterialElement->IsBoundWithMaterialInstance())
    {
        OwnerPtr<IGraphicsMaterialData> pMaterialData = pMaterialElement->GetGraphicsMaterialDataFromMaterialInstance();
        DBG_WARN_AND_RETURN_VOID_UNLESS(pMaterialData, L"materialId 有效却无法得到 IGraphicsMaterialData 数据",L"GDMPLab",L"2024-03-30");

        // 查找与图形材质使用相同材质实例的无类别图形材质
        std::vector<ElementId> materialInstanceIds = pDoc->GetGraphicsMaterialManager()->GetAllGraphicsMaterialsBindingWithTheSameMaterialInstance(pMaterialElement);
        UniIdentity noCategoryMaterialInstanceUid = UniIdentity::InvalidUid;
        for (ElementId id : materialInstanceIds)
        {
            IGraphicsMaterial* pMaterialInstance = quick_cast<IGraphicsMaterial>(pDoc->GetElement(id));
            DBG_WARN_AND_CONTINUE_UNLESS(pMaterialInstance, L"pMaterialInstance 为空",L"GDMPLab",L"2024-03-30");
            if (pMaterialInstance->GetTargetCategoryUid() == UniIdentity::InvalidUid)
            {
                noCategoryMaterialInstanceUid = pMaterialInstance->GetBasicInformation()->GetElementUid();
                if (!noCategoryMaterialInstanceUid.IsValid())
                {
                    noCategoryMaterialInstanceUid = GuidUtils::Generate();
                    pMaterialInstance->GetBasicInformation()->SetElementUid(noCategoryMaterialInstanceUid);
                }
                break;
            }
        }
        DBG_WARN_AND_RETURN_VOID_UNLESS(noCategoryMaterialInstanceUid.IsValid(), L"为什么没拿到使用相同材质实例的无类别图形材质？",L"GDMPLab",L"2024-03-30");

        std::wstring materialInstanceName;
        bool bVal = pMaterialElement->GetMaterialInstanceName(materialInstanceName);
        DBG_WARN_AND_RETURN_VOID_UNLESS(bVal, L"pMaterialElement->GetMaterialInstanceName(materialInstanceName) 返回 false",L"GDMPLab",L"2024-03-30");

        std::wstring diffuseTexureFilePath = pMaterialData->GetDiffuseTextueFilePath();
        std::wstring diffuseTexureFileName = FilePath(diffuseTexureFilePath).GetFileName();
        std::wstring copyDiffuseTexureFilePath = LogManager::Get()->GetAbsolutePathForLog(diffuseTexureFileName);
        if (!FileInfo(copyDiffuseTexureFilePath).Exists())
        {
            DBG_WARN_AND_RETURN_VOID_UNLESS(FileInfo(diffuseTexureFilePath).IsFile(), L"没有找到纹理文件：" + diffuseTexureFilePath,L"GDMPLab",L"2024-03-30");
            bool isOk = FileUtility::CopyFileW(diffuseTexureFilePath, copyDiffuseTexureFilePath);
            DBG_WARN_AND_RETURN_VOID_UNLESS(isOk, L"纹理文件拷贝失败",L"GDMPLab",L"2024-03-30");
        }

        paramsMap[L"CategoryUid"] = categoryUniIdentity.GetGuidString();
        paramsMap[L"NoCategoryMaterialInstanceUid"] = noCategoryMaterialInstanceUid.GetGuidString();
        paramsMap[L"Name"] = materialInstanceName;

        paramsMap[L"Ambient_R"] = pMaterialData->GetAmbient().R;
        paramsMap[L"Ambient_G"] = pMaterialData->GetAmbient().G;
        paramsMap[L"Ambient_B"] = pMaterialData->GetAmbient().B;
        paramsMap[L"Ambient_A"] = pMaterialData->GetAmbient().A;

        paramsMap[L"Diffuse_R"] = pMaterialData->GetDiffuse().R;
        paramsMap[L"Diffuse_G"] = pMaterialData->GetDiffuse().G;
        paramsMap[L"Diffuse_B"] = pMaterialData->GetDiffuse().B;
        paramsMap[L"Diffuse_A"] = pMaterialData->GetDiffuse().A;

        paramsMap[L"Specular_R"] = pMaterialData->GetSpecular().R;
        paramsMap[L"Specular_G"] = pMaterialData->GetSpecular().G;
        paramsMap[L"Specular_B"] = pMaterialData->GetSpecular().B;
        paramsMap[L"Specular_A"] = pMaterialData->GetSpecular().A;

        paramsMap[L"Transparency"] = pMaterialData->GetTransparency();
        paramsMap[L"Shininess"] = pMaterialData->GetShininess();
        paramsMap[L"IsDiffuseTextureEnabled"] = pMaterialData->IsDiffuseTextureEnabled();
        paramsMap[L"DiffuseTexureFileName"] = diffuseTexureFileName;

        paramsMap[L"DiffuseTexureOffset_X"] = pMaterialData->GetDiffuseTexureOffset().X();
        paramsMap[L"DiffuseTexureOffset_Y"] = pMaterialData->GetDiffuseTexureOffset().Y();

        paramsMap[L"DiffuseTexureRotation"] = pMaterialData->GetDiffuseTexureRotation();
        paramsMap[L"DiffuseTexureScale_X"] = pMaterialData->GetDiffuseTexureScale().X();
        paramsMap[L"DiffuseTexureScale_Y"] = pMaterialData->GetDiffuseTexureScale().Y();
    }
    else
    {
        paramsMap[L"CategoryUid"] = categoryUniIdentity.GetGuidString();
    }
    OwnerPtr<gcmp::IJournalCommandData> opJournalCmdData = IJournalCommandData::Create();
    DBG_WARN_AND_RETURN_VOID_UNLESS(opJournalCmdData, L"opJournalCmdData空指针",L"GDMPLab",L"2024-03-30");
    opJournalCmdData->SetCommandName(cmdName);
    opJournalCmdData->SetParameterArray(paramsMap);
    gcmp::JournalUtils::RecordCommand(TransferOwnership(opJournalCmdData));
}


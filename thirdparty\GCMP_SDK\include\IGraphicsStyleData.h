// Owner: zhuxf
// Co-Owner:
// Reviewed

#pragma once

#include "GcmpModelInterface.h"
#include "Color.h"
#include "IGraphicsStyleDataValiditySettings.h"
#include "NdbDataSchema.h"
#include "IColorIndexPalette.h"
#include "IGraphicsStyleDataValiditySettings.h"

namespace gcmp
{
    /// \brief 线宽类型
    SERIALIZABLE_ENUM_CLASS(LineWidthMode)
    {
        PIXEL = 0,         ///< 定像素线
        WORLD              ///< 定宽度线
    };

    /// \brief 智能颜色
    SERIALIZABLE_ENUM_CLASS(SmartColorMode)
    {
        Invalid = 0,                       ///< 不使用智能颜色
        SmartUseBackgroundColor = 1,       ///< 智能颜色 - 使用背景色
        SmartAntiBackgroundColor = 2       ///< 智能颜色 - 使用背景色的反色
    };

    /// \brief 图形样式数据，即图形样式包含的字段
    class GCMP_MODEL_INTERFACE_EXPORT IGraphicsStyleData
    {
        DEFINE_CAST_BASE(gcmp::IGraphicsStyleData);
    public:
        /// \brief 析构函数
        virtual ~IGraphicsStyleData() {}

#pragma region static methods
        /// \brief 创建图形样式数据
        ///
        /// 创建新的图形样式数据后，建议调用 Reset() 初始化一下
        /// \return 新建的图形样式数据
        static OwnerPtr<IGraphicsStyleData> Create();
#pragma endregion static methods

        /// \brief 获取最终的颜色：目前只包含真彩色和索引色
        ///
        /// 只影响点或投影面的颜色
        /// \return 颜色
        virtual Color GetFinalColor() const = 0;

        /// \brief 获取当前索引表对应的最终的投影线颜色：目前只包含真彩色和索引色
        ///
        /// \return 投影线颜色
        virtual Color GetFinalProjectionLineColor() const = 0;

        /// \brief 获取当前索引表对应的最终的截面线颜色：目前只包含真彩色和索引色
        ///
        /// \return 截面线颜色
        virtual Color GetFinalSectionLineColor() const = 0;

        /// \brief 获取当前索引表对应的最终的隐藏线颜色：目前只包含真彩色和索引色
        ///
        /// \return 隐藏线颜色
        virtual Color GetFinalHiddenLineColor() const = 0;

        /// \brief 获取当前索引表对应的最终的投影面填充颜色：目前只包含真彩色和索引色
        ///
        /// \return 投影面填充颜色
        virtual Color GetFinalProjectionFaceHatchColor() const = 0;

        /// \brief 获取当前索引表对应的最终的截面填充颜色：目前只包含真彩色和索引色
        ///
        /// \return 截面填充颜色
        virtual Color GetFinalSectionFaceHatchColor() const = 0;

        /// \brief 获取当前索引表对应的最终的截面颜色：目前只包含真彩色和索引色
        ///
        /// \return 截面颜色
        virtual Color GetFinalSectionFaceColor() const = 0;

        /// \brief 获取当前索引表对应的最终的面的颜色：目前只包含真彩色和索引色
        ///
        /// \param isSection 是否是截面
        /// \return 面颜色
        virtual Color GetFinalFaceColor(bool isSection) const = 0;

        /// \brief 设置颜色
        ///
        /// 只影响点或投影面的颜色
        /// \param color 目标颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetColor(const Color& color) = 0;

        /// \brief 获取颜色
        ///
        /// 只影响点或投影面的颜色
        /// \return 颜色
        virtual Color GetColor() const = 0;

        /// \brief 设置颜色索引
        ///
        /// 只影响点或投影面的颜色索引
        /// \param index 目标颜色索引[0-255] 
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetColorIndex(Int16 index) = 0;

        /// \brief 获取颜色索引
        ///
        /// 只影响点或投影面的颜色索引
        /// \return 颜色索引
        virtual Int16 GetColorIndex() const = 0;

        /// \brief 设置透明度
        /// \param transparency 目标透明度，取值范围 [0,1]，0-完全透明，1-完全不透明
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetTransparency(double transparency) = 0;

        /// \brief 获取透明度
        /// \return 透明度
        virtual double GetTransparency() const = 0;
        
        /// \brief 设置投影线的颜色
        /// \param color 目标颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLineColor(const Color& color) = 0;

        /// \brief 获取投影线的颜色
        /// \return 颜色
        virtual Color GetProjectionLineColor() const = 0;

        /// \brief 设置投影线的颜色索引
        /// \param index 目标颜色索引[0-255]
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLineColorIndex(Int16 index) = 0;

        /// \brief 获取投影线的颜色索引
        /// \return 颜色索引
        virtual Int16 GetProjectionLineColorIndex() const = 0;

        /// \brief 设置是否采用投影线的颜色本身的alpha值
        /// \param enable 是否采用
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& EnableProjectionLineColorAlpha(bool enable) = 0;

        /// \brief 获取投影线的颜色的alpha值是否采用
        /// \return  是否采用投影线的颜色本身的alpha值
        virtual bool IsProjectionLineColorAlphaEnabled() const = 0;


        /// \brief 设置投影线的宽度
        /// \param width 目标宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLineWidth(double width) = 0;

        /// \brief 获取投影线的宽度
        ///
        /// 当线宽模式是定像素线时，返回原先设置的宽度值前，将做向下取整操作
        /// 当线宽模式是定宽度线时，直接返回原先设置的宽度值
        /// \return 线宽
        virtual double GetProjectionLineWidth() const = 0;
        
        /// \brief 设置投影线的打印宽度
        ///
        /// 打印宽度:
        ///   >= 0 且打印宽度字段有效: 导出时直接使用这个宽度
        ///   = -1 或打印宽度字段无效: 导出时会使用像素宽度回调提示用户给出对应打印宽度
        ///   < 0 且 != -1: 无效输入，无法设置成功
        /// \param width 打印宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLinePrintWidth(double width) = 0;

        /// \brief 获取投影线的打印宽度
        /// \return 打印宽度
        virtual double GetProjectionLinePrintWidth() const = 0;
        
        /// \brief 设置投影线的线型, 对应的opengl接口是glLineStipple
        ///
        /// 注意， 这个接口产生的线型是通过渲染做的，只是显示上是虚线，导出和鼠标选中还是按实线处理。且opengl高版本是不支持这个接口的， 尽量不要使用。
        /// ， 此接口基于SetProjectionLineTypeName离散结果起效，且只对定像素有效, 建议不要和LineTypeName同时使用。 
        /// \param factor表示重复因子， 一般是1， pattern代表图案， 如0xff00
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLineStipple(int factor, int pattern) = 0;
        
        /// \brief 获取投影线的线型, 对应的opengl接口是glLineStipple
        virtual void GetProjectionLineStipple(int& factor, int& pattern) const = 0;

        /// \brief 设置投影线的线型
        /// \param lineTypeName 目标线型名
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLineTypeName(const std::wstring& lineTypeName) = 0;

        /// \brief 获取投影线的线型
        /// \return 线型名
        virtual const std::wstring& GetProjectionLineTypeName() const = 0;
                        
        /// \brief 设置截面线的颜色
        /// \param color 目标颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLineColor(const Color& color) = 0;

        /// \brief 获取截面线的颜色
        /// \return 线颜色
        virtual Color GetSectionLineColor() const = 0;

        /// \brief 设置截面线的颜色索引
        /// \param index 目标颜色索引[0-255]
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLineColorIndex(Int16 index) = 0;

        /// \brief 获取截面线的颜色索引
        /// \return 线颜色索引
        virtual Int16 GetSectionLineColorIndex() const = 0;

        /// \brief 设置截面线的宽度
        /// \param width 目标宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLineWidth(double width) = 0;

        /// \brief 获取截面线的宽度
        ///
        /// 当线宽模式是定像素线时，返回原先设置的宽度值前，将做向下取整操作
        /// 当线宽模式是定宽度线时，直接返回原先设置的宽度值
        /// \return 线宽
        virtual double GetSectionLineWidth() const = 0;
        
        /// \brief 设置截面线的打印宽度
        ///
        /// 打印宽度:
        ///   >= 0 且打印宽度字段有效: 导出时直接使用这个宽度
        ///   = -1 或打印宽度字段无效: 导出时会使用像素宽度回调提示用户给出对应打印宽度
        ///   < 0 且 != -1 : 无效输入，无法设置成功
        /// \param width 打印宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLinePrintWidth(double width) = 0;

        /// \brief 获取截面线的打印宽度
        /// \return 打印宽度
        virtual double GetSectionLinePrintWidth() const = 0;

        /// \brief 设置截面线的线型, 对应的opengl接口是glLineStipple
        ///
        /// 注意， 这个接口产生的线型是通过渲染做的，只是显示上是虚线，导出和鼠标选中还是按实线处理。且opengl高版本是不支持这个接口的， 尽量不要使用。
        /// ， 此接口基于SetSectionLineTypeName离散结果起效，且只对定像素有效, 建议不要和LineTypeName同时使用。 
        /// \param factor表示重复因子， 一般是1， pattern代表图案， 如0xff00
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLineStipple(int factor, int pattern) = 0;

        /// \brief 获取投影线的线型, 对应的opengl接口是glLineStipple
        virtual void GetSectionLineStipple(int& factor, int& pattern) const = 0;

        /// \brief 设置截面线的线型
        /// \param name 目标线型名
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLineTypeName(const std::wstring& name) = 0;

        /// \brief 获取截面线的线型
        /// \return 线型名
        virtual const std::wstring& GetSectionLineTypeName() const = 0;

        /// \brief 设置隐藏线的颜色
        /// \param color 目标颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineColor(const Color& color) = 0;

        /// \brief 获取隐藏线的颜色
        /// \return 线颜色
        virtual Color GetHiddenLineColor() const = 0;

        /// \brief 设置隐藏线的颜色索引
        /// \param index 目标颜色索引[0-255] 
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineColorIndex(Int16 index) = 0;

        /// \brief 获取隐藏线的颜色索引   
        /// \return 线颜色索引
        virtual Int16 GetHiddenLineColorIndex() const = 0;

        /// \brief 设置隐藏线的宽度
        /// \param width 目标宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineWidth(double width) = 0;

        /// \brief 获取隐藏线的宽度
        ///
        /// 当线宽模式是定像素线时，返回原先设置的宽度值前，将做向下取整操作
        /// 当线宽模式是定宽度线时，直接返回原先设置的宽度值
        /// \return 线宽
        virtual double GetHiddenLineWidth() const = 0;
        
        /// \brief 设置隐藏线的打印宽度
        ///
        /// 打印宽度:
        ///   >= 0 且打印宽度字段有效: 导出时直接使用这个宽度
        ///   = -1 或打印宽度字段无效: 导出时会使用像素宽度回调提示用户给出对应打印宽度
        ///   < 0 且 != -1: 无效输入，无法设置成功
        /// \param width 打印宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLinePrintWidth(double width) = 0;

        /// \brief 获取隐藏线的打印宽度
        /// \return 打印宽度
        virtual double GetHiddenLinePrintWidth() const = 0;
        
        /// \brief 设置隐藏线的线型, 对应的opengl接口是glLineStipple
        ///
        /// 注意， 这个接口产生的线型是通过渲染做的，只是显示上是虚线，导出和鼠标选中还是按实线处理。且opengl高版本是不支持这个接口的， 尽量不要使用。
        /// ， 此接口基于SetHiddenLineTypeName离散结果起效，且只对定像素有效, 建议不要和LineTypeName同时使用。 
        /// \param factor表示重复因子， 一般是1， pattern代表图案， 如0xff00
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineStipple(int factor, int pattern) = 0;

        /// \brief 获取投影线的线型, 对应的opengl接口是glLineStipple
        virtual void GetHiddenLineStipple(int& factor, int& pattern) const = 0;

        /// \brief 设置隐藏线的线型
        /// \param name 目标线型名
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineTypeName(const std::wstring& name) = 0;

        /// \brief 获取隐藏线的线型
        /// \return 线型名
        virtual const std::wstring& GetHiddenLineTypeName() const = 0;

        /// \brief 设置投影面填充颜色
        /// \param color 面填充颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchColor(const Color& color) = 0;

        /// \brief 获取投影面填充颜色
        /// \return 面填充颜色
        virtual Color GetProjectionFaceHatchColor() const = 0;

        /// \brief 设置投影面填充颜色索引
        /// \param index 面填充颜色索引[0-255] 
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchColorIndex(Int16 index) = 0;

        /// \brief 获取投影面填充颜色索引
        /// \return 面填充颜色索引
        virtual Int16 GetProjectionFaceHatchColorIndex() const = 0;
        
        /// \brief 设置是否采用投影面填充颜色本身的alpha值
        /// \param enable 是否采用，默认是 true
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& EnableProjectionFaceHatchColorAlpha(bool enable) = 0;

        /// \brief 获取投影面填充颜色的alpha值是否采用
        /// \return 是否采用投影面填充颜色本身的alpha值
        virtual bool IsProjectionFaceHatchColorAlphaEnabled() const = 0;
        
        /// \brief 设置投影面填充图案线宽
        /// \param lineWidth 投影面填充图案线宽, 目前使用像素模式，double类型是为了以后支持世界单位模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchLineWidth(double lineWidth) = 0;

        /// \brief 获取投影面填充图案线宽
        /// \return 面填充图案线宽
        virtual double GetProjectionFaceHatchLineWidth() const = 0;

        /// \brief 设置投影面填充图案线的打印宽度
        ///
        /// 打印宽度:
        ///   >= 0 且打印宽度字段有效: 导出时直接使用这个宽度
        ///   = -1 或打印宽度字段无效: 导出时会使用像素宽度回调提示用户给出对应打印宽度
        ///   < 0 且 != -1: 无效输入，无法设置成功
        /// \param lineWidth 打印宽度
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchLinePrintWidth(double lineWidth) = 0;

        /// \brief 获取投影面填充图案线的打印宽度
        /// \return 打印宽度
        virtual double GetProjectionFaceHatchLinePrintWidth() const = 0;

        /// \brief 设置投影面填充模式
        /// \param pattern 面填充模式名
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchPattern(const std::wstring& pattern) = 0;

        /// \brief 获取投影面填充模式
        /// \return 面填充模式名
        virtual const std::wstring& GetProjectionFaceHatchPattern() const = 0;

        /// \brief 设置投影面填充缩放
        /// \param scale 面填充缩放量
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchScale(double scale) = 0;

        /// \brief 获取投影面填充缩放
        /// \return 面填充缩放量
        virtual double GetProjectionFaceHatchScale() const = 0;

        /// \brief 设置投影面填充旋转
        /// \param rotation 面填充旋转量
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchRotation(double rotation) = 0;

        /// \brief 获取投影面填充旋转
        /// \return 面填充旋转量
        virtual double GetProjectionFaceHatchRotation() const = 0;
        
        /// \brief 设置截面填充颜色
        /// \param color 面填充颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceHatchColor(const Color& color) = 0;

        /// \brief 获取截面填充颜色
        /// \return 面填充颜色
        virtual Color GetSectionFaceHatchColor() const = 0;

        /// \brief 设置截面填充颜色索引
        /// \param index 面填充颜色索引[0-255]
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceHatchColorIndex(Int16 index) = 0;

        /// \brief 获取截面填充颜色索引
        /// \return 面填充颜色索引
        virtual Int16 GetSectionFaceHatchColorIndex() const = 0;
        
        /// \brief 设置是否采用截面填充颜色本身的alpha值
        /// \param enable 是否采用，默认是 true
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& EnableSectionFaceHatchColorAlpha(bool enable) = 0;

        /// \brief 获取截面填充颜色的alpha值是否采用
        /// \return 是否采用截面填充颜色本身的alpha值
        virtual bool IsSectionFaceHatchColorAlphaEnabled() const = 0;
        
        /// \brief 设置截面填充模式
        /// \param pattern 面填充模式名
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceHatchPattern(const std::wstring& pattern) = 0;

        /// \brief 获取截面填充模式
        /// \return 面填充模式名
        virtual const std::wstring& GetSectionFaceHatchPattern() const = 0;

        /// \brief 设置截面填充缩放
        /// \param scale 面填充缩放量
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceHatchScale(double scale) = 0;

        /// \brief 获取截面填充缩放
        /// \return 面填充缩放量
        virtual double GetSectionFaceHatchScale() const = 0;  

        /// \brief 设置截面填充旋转
        /// \param rotation 面填充旋转量
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceHatchRotation(double rotation) = 0;

        /// \brief 获取截面填充旋转
        /// \return 面填充旋转量
        virtual double GetSectionFaceHatchRotation() const = 0;

        /// \brief 获取投影线线宽模式，定像素线还是定宽度线
        /// \return 线宽模式
        virtual LineWidthMode GetProjectionLineWidthMode() const = 0;

        /// \brief 设置投影线线宽模式，定像素线还是定宽度线
        /// \param lineWidthMode 线宽模式，定像素线还是定宽度线
        virtual void SetProjectionLineWidthMode(LineWidthMode lineWidthMode) = 0;

        /// \brief 获取投影线线型缩放因子
        ///           注意：gcmp画布上显示的线型，默认是pStyleData->GetProjectionLineTypeScale() / pModelView→GetViewScale()
        /// \return 线型缩放因子
        virtual double GetProjectionLineTypeScale() const = 0;

        /// \brief 设置投影线线型缩放因子
        ///
        ///  缩放因子须大于0，如果小于0，将被重置成1
        ///  注意：gcmp画布上显示的线型，默认是pStyleData->GetProjectionLineTypeScale() / pModelView→GetViewScale()，
        ///            如设置显示比例无关线型，可以通过视图样式重载实现（视图比例改变时，始终抵消"/ pModelView→GetViewScale()"影响）
        /// \param scale 线型缩放因子
        virtual IGraphicsStyleData& SetProjectionLineTypeScale(double scale) = 0;

        /// \brief 获取截面线线宽模式，定像素线还是定宽度线
        /// \return 线宽模式
        virtual LineWidthMode GetSectionLineWidthMode() const = 0;

        /// \brief 设置截面线线宽模式，定像素线还是定宽度线
        /// \param lineWidthMode 线宽模式，定像素线还是定宽度线
        virtual void SetSectionLineWidthMode(LineWidthMode lineWidthMode) = 0;

        /// \brief 获取截面线线型缩放因子
        /// \return 线型缩放因子
        virtual double GetSectionLineTypeScale() const = 0;

        /// \brief 设置截面线线型缩放因子
        ///
        ///  缩放因子须大于0，如果小于0，将被重置成1
        /// \param scale 线型缩放因子
        virtual void SetSectionLineTypeScale(double scale) = 0;

        /// \brief 获取隐藏线线宽模式，定像素线还是定宽度线
        /// \return 线宽模式
        virtual LineWidthMode GetHiddenLineWidthMode() const = 0;

        /// \brief 设置隐藏线线宽模式，定像素线还是定宽度线
        /// \param lineWidthMode 线宽模式，定像素线还是定宽度线
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineWidthMode(LineWidthMode lineWidthMode) = 0;

        /// \brief 获取隐藏线线型缩放因子
        /// \return 线型缩放因子
        virtual double GetHiddenLineTypeScale() const = 0;

        /// \brief 设置隐藏线线型缩放因子
        ///
        ///  缩放因子须大于0，如果小于0，将被重置成1
        /// \param scale 线型缩放因子
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineTypeScale(double scale) = 0;
                
        /// \brief 重载==符，判断两个图形样式数据是否相同
        /// \param another 需要对比的目标图形样式数据
        /// \return true-相同，false-不同
        virtual bool operator == (const IGraphicsStyleData & another) const = 0;

        /// \brief 重载!=符，判断两个图形样式数据是否不同
        /// \param another 需要对比的目标图形样式数据
        /// \return true-不同，false-相同
        virtual bool operator != (const IGraphicsStyleData & another) const = 0;

        /// \brief 重载<符，判断两个图形样式数据大小关系
        /// \param another 需要对比的目标图形样式数据
        /// \return true-当前图形样式数据小于目标，false-当前图形样式数据大于等于目标
        virtual bool operator < (const IGraphicsStyleData & another) const = 0;

        /// \brief 克隆
        ///
        /// 将自己复制一份
        /// \return 自己的副本
        virtual OwnerPtr<IGraphicsStyleData> Clone() const = 0;
    
        /// \brief 将图形样式数据的各字段重置为默认值
        /// \param color 点或投影面的颜色
        /// \param transparency 透明度
        /// \param projectionLineColor 投影线颜色
        /// \param projectionLineColorAlphaEnable 投影线颜色本身的alpha值是否起效
        /// \param projectionLineWidth 投影线宽度
        /// \param projectionLineTypeName 投影线的线型名
        /// \param sectionLineColor 截面线颜色
        /// \param sectionLineWidth 截面线宽度
        /// \param sectionLineTypeName 截面线的线型名
        /// \param projectionFaceHatchColor 投影面填充颜色
        /// \param projectionFaceHatchPattern 投影面填充模式
        /// \param projectionFaceHatchScale 投影面填充缩放
        /// \param projectionFaceHatchRotation 投影面填充旋转
        /// \param sectionFaceHatchColor 截面填充颜色
        /// \param sectionFaceHatchPattern 截面填充模式
        /// \param sectionFaceHatchScale 截面填充缩放
        /// \param sectionFaceHatchRotation 截面填充旋转
        /// \param projectionLineWidthMode 投影线线宽模式
        /// \param sectionLineWidthMode 截面线线宽模式
        /// \param projectionLineTypeScale 投影线线型缩放因子
        /// \param sectionLineTypeScale 截面线线型缩放因子
        /// \param projectionFaceHatchLineWdith 投影面填充图案线宽， 目前使用像素模式，double类型是为了以后支持世界单位模式
        /// \param isProjectionColorSmart 投影线颜色是否跟随背景改变
        /// \param isSectionColorSmart 截面线颜色是否跟随背景改变
        /// \param isColorSmart 设置颜色是否是smart( 只影响点或面的颜色)
        /// \param SmartColorMode 设置智能颜色模式
        /// \param isProjectionFaceHatchColorSmart 设置获取投影面填充颜色是否是smart
        /// \param isSectionFaceHatchColorSmart 设置获取截面填充颜色是否是smart
        /// \param hiddenLineColor 隐藏线颜色
        /// \param hiddenLineWidth 隐藏线宽度
        /// \param hiddenLineTypeName 隐藏线的线型名
        /// \param hiddenLineWidthMode 隐藏线线宽模式
        /// \param isHiddenLineColorSmart 隐藏线颜色是否跟随背景改变
        /// \param hiddenLineTypeScale 隐藏线线型缩放因子
        /// \param sectionFaceColor 截面颜色
        /// \param sectionFaceColorSmartColorMode 截面智能颜色模式
        /// \param pGraphicsStyleDataValiditySettings 图形样式数据中各属性的有效性设置，传入空则全部有效
        ///\param projectionLineFactor 投影线线型factor
        ///\param projectionLinePattern 投影线线型pattern
        ///\param sectionLineFactor 截面线线型factor
        ///\param sectionLinePattern  截面线线型pattern
        ///\param hiddenLineFactor 隐藏线线型factor
        ///\param hiddenLinePattern 隐藏线线型pattern
        /// \param projectionFaceHatchColorAlphaEnabled 投影面填充颜色本身的alpha值是否起效
        /// \param sectionFaceHatchColorAlphaEnabled 截面填充颜色本身的alpha值是否起效
        /// \return 被重置的图形样式数据自身
        IGraphicsStyleData& Reset(
            const Color& color = Color::Gray,
            double transparency = 1.0,
            const Color& projectionLineColor = Color::Black,
            double projectionLineWidth = 1.0,
            const std::wstring& projectionLineTypeName = L"",
            const Color& sectionLineColor = Color::Black,
            double sectionLineWidth = 1.0,
            const std::wstring& sectionLineTypeName = L"",
            const Color& projectionFaceHatchColor = Color::Black,
            const std::wstring& projectionFaceHatchPattern = L"",
            double projectionFaceHatchScale = 1.0,
            double projectionFaceHatchRotation = 0.0,
            const Color& sectionFaceHatchColor = Color::Black,
            const std::wstring& sectionFaceHatchPattern = L"",
            double sectionFaceHatchScale = 1.0,
            double sectionFaceHatchRotation = 0.0,
            LineWidthMode projectionLineWidthMode = LineWidthMode::PIXEL,
            LineWidthMode sectionLineWidthMode = LineWidthMode::PIXEL,
            double projectionLineTypeScale = 1.0,
            double sectionLineTypeScale = 1.0,
            double projectionFaceHatchLineWdith = 1.0,
            SmartColorMode projectionLineColorSmart = SmartColorMode::Invalid,
            SmartColorMode sectionLineColorSmart = SmartColorMode::Invalid,
            SmartColorMode smartColorMode = SmartColorMode::Invalid,
            bool projectionLineColorAlphaEnabled = false,
            SmartColorMode projectionFaceHatchColorSmart = SmartColorMode::Invalid,
            SmartColorMode sectionFaceHatchColorSmart = SmartColorMode::Invalid,
            const Color& hiddenLineColor = Color::Black,
            double hiddenLineWidth = 1.0,
            const std::wstring& hiddenLineTypeName = L"DASHED",
            LineWidthMode hiddenLineWidthMode = LineWidthMode::PIXEL,
            double hiddenLineTypeScale = 1.0,
            SmartColorMode hiddenLineColorSmart = SmartColorMode::Invalid,
            const Color& sectionFaceColor = Color::Gray,
            SmartColorMode sectionFaceColorSmartColorMode = SmartColorMode::Invalid,
            const IGraphicsStyleDataValiditySettings* pGraphicsStyleDataValiditySettings = nullptr,
            int projectionLineFactor = 0,
            int projectionLinePattern = 0,
            int sectionLineFactor = 0,
            int sectionLinePattern = 0,
            int hiddenLineFactor = 0,
            int hiddenLinePattern = 0,
            Int16 colorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            Int16 projectionLineColorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            Int16 sectionLineColorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            Int16 projectionFaceHatchColorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            Int16 sectionFaceHatchColorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            Int16 hiddenLineColorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            Int16 sectionFaceColorIndex = IColorIndexPalette::INVALID_COLOR_INDEX,
            bool projectionFaceHatchColorAlphaEnabled = true,
            bool sectionFaceHatchColorAlphaEnabled = true,
            double projectionLinePrintWidth = -1.0,
            double sectionLinePrintWidth = -1.0,
            double hiddenLinePrintWidth = -1.0,
            double projectionFaceHatchLinePrintWidth = -1.0
        );

        /// \brief 获得点和投影面智能颜色模式，见enum SmartColorMode
        /// \return 获得的SmartColorMode
        virtual SmartColorMode GetSmartColorMode() const = 0;

        /// \brief 设置点和投影面智能颜色模式，见enum SmartColorMode
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获得投影线智能颜色模式，见enum SmartColorMode
        /// \return 获得的投影线SmartColorMode
        virtual SmartColorMode GetProjectionLineSmartColorMode() const = 0;

        /// \brief 设置投影线智能颜色模式，见enum SmartColorMode
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionLineSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获得截面线智能颜色模式，见enum SmartColorMode
        /// \return 获得的截面线SmartColorMode
        virtual SmartColorMode GetSectionLineSmartColorMode() const = 0;

        /// \brief 设置截面线智能颜色模式，见enum SmartColorMode
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionLineSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获得隐藏线智能颜色模式，见enum SmartColorMode
        /// \return 获得的隐藏线SmartColorMode
        virtual SmartColorMode GetHiddenLineSmartColorMode() const = 0;

        /// \brief 设置隐藏线智能颜色模式，见enum SmartColorMode
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetHiddenLineSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获得投影面填充智能颜色模式，见enum SmartColorMode
        /// \return 获得的投影面填充SmartColorMode
        virtual SmartColorMode GetProjectionFaceHatchSmartColorMode() const = 0;

        /// \brief 设置投影面填充智能颜色模式，见enum SmartColorMode
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetProjectionFaceHatchSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获得截面填充智能颜色模式，见enum SmartColorMode
        /// \return 获得的截面填充SmartColorMode
        virtual SmartColorMode GetSectionFaceHatchSmartColorMode() const = 0;

        /// \brief 设置截面填充智能颜色模式，见enum SmartColorMode
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceHatchSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获取截面颜色
        ///
        /// \return 颜色
        virtual Color GetSectionFaceColor() const = 0;

        /// \brief 设置截面颜色
        ///
        /// \param color 目标颜色
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceColor(const Color& color) = 0;

        /// \brief 获取截面颜色索引
        ///
        /// \return 颜色索引
        virtual Int16 GetSectionFaceColorIndex() const = 0;

        /// \brief 设置截面颜色索引
        ///
        /// \param index 目标颜色索引[0-255]
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceColorIndex(Int16 index) = 0;

        /// \brief 获取面颜色
        ///
        /// \param isSection 是否是截面
        /// \return 颜色
        virtual Color GetFaceColor(bool isSection) const = 0;

        /// \brief 获取面颜色索引
        ///
        /// \param isSection 是否是截面
        /// \return 颜色索引
        virtual Int16 GetFaceColorIndex(bool isSection) const = 0;
        
        /// \brief 获得截面智能颜色模式，见enum SmartColorMode
        /// \return 获得的SmartColorMode
        virtual SmartColorMode GetSectionFaceColorSmartColorMode() const = 0;

        /// \brief 设置截面智能颜色模式，见enum SmartColorMode
        ///
        /// \param isSection 是否是截面
        /// \param smartColorMode 需设置的智能颜色模式
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetSectionFaceColorSmartColorMode(const SmartColorMode& smartColorMode) = 0;

        /// \brief 获得面的智能颜色模式，见enum SmartColorMode
        /// \return 获得的SmartColorMode
        virtual SmartColorMode GetFaceSmartColorMode(bool isSection) const = 0;

        /// \brief 获取图形样式数据的有效性设置
        /// \return 图形样式数据的有效性设置
        virtual OwnerPtr<IGraphicsStyleDataValiditySettings> GetDataValiditySettings() const = 0;

        /// \brief 设置图形样式数据的有效性
        /// \param graphicsStyleDataValiditySettings：图形样式数据的有效性设置
        /// \return 被修改的图形样式数据自身
        virtual IGraphicsStyleData& SetDataValiditySettings(const IGraphicsStyleDataValiditySettings& graphicsStyleDataValiditySettings) = 0;

    };

}




